# 自适应学习服务数据标注模块 - 数据库设计

## 1. 设计概述

### 1.1 设计原则
- **数据完整性**：确保数据的一致性和准确性
- **性能优化**：针对高频查询场景进行索引优化
- **可扩展性**：支持大规模数据存储和查询
- **版本控制**：支持数据的版本管理和回滚
- **审计追踪**：完整记录数据变更历史

### 1.2 数据库选择
- **主数据库**：PostgreSQL 15+
- **扩展功能**：LTREE（层级数据）、pgRouting（图算法）
- **缓存层**：Redis（会话、缓存）
- **搜索引擎**：PostgreSQL全文搜索

## 2. 数据库模块划分

| 模块 | 主要表 | 作用 |
|------|--------|------|
| **用户与权限** | `users`, `user_roles`, `permissions` | 账号管理、角色权限控制 |
| **知识本体** | `knowledge_points`, `prerequisite_relation` | 知识点层级和先修关系 |
| **题库管理** | `questions`, `question_assets`, `item_param` | 题目内容、媒资、IRT参数 |
| **关联映射** | `item_kp_map`, `question_relation` | 题-知识点映射、题-题关系 |
| **知识空间** | `knowledge_state`, `state_transition` | 知识状态和转移关系 |
| **标注工作流** | `annotation_tasks`, `annotation_logs` | 任务管理、操作审计 |
| **版本管理** | `question_versions`, `kp_versions` | 数据版本控制 |
| **模型参数** | `skill_param`, `model_config` | BKT参数、模型配置 |

## 3. 核心表结构设计

### 3.1 用户与权限模块

#### 3.1.1 users - 系统用户表
```sql
CREATE TABLE users (
    user_id BIGSERIAL PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role VARCHAR(16) NOT NULL CHECK (role IN ('admin', 'annotator', 'reviewer', 'viewer', 'engine')),
    full_name TEXT,
    email TEXT UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_login TIMESTAMPTZ,
    login_count INTEGER DEFAULT 0
);

CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
```

#### 3.1.2 user_sessions - 用户会话表
```sql
CREATE TABLE user_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    ip_address INET,
    user_agent TEXT
);

CREATE INDEX idx_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
```

### 3.2 知识本体模块

#### 3.2.1 knowledge_points - 知识点定义表
```sql
CREATE TABLE knowledge_points (
    kp_id SERIAL PRIMARY KEY,
    parent_id INTEGER REFERENCES knowledge_points(kp_id),
    name TEXT NOT NULL,
    code TEXT UNIQUE,
    path LTREE,
    description TEXT,
    is_leaf BOOLEAN DEFAULT FALSE,
    difficulty_level SMALLINT CHECK (difficulty_level BETWEEN 1 AND 5),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by BIGINT REFERENCES users(user_id)
);

CREATE INDEX idx_kp_path_gist ON knowledge_points USING GIST(path);
CREATE INDEX idx_kp_code ON knowledge_points(code);
CREATE INDEX idx_kp_parent ON knowledge_points(parent_id);
CREATE INDEX idx_kp_leaf ON knowledge_points(is_leaf);
```

#### 3.2.2 prerequisite_relation - 知识点先修关系表
```sql
CREATE TABLE prerequisite_relation (
    pre_kp_id INTEGER REFERENCES knowledge_points(kp_id),
    post_kp_id INTEGER REFERENCES knowledge_points(kp_id),
    source SMALLINT DEFAULT 0 CHECK (source IN (0, 1, 2)), -- 0=expert, 1=algorithm, 2=import
    confidence NUMERIC(3,2) CHECK (confidence BETWEEN 0 AND 1),
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (pre_kp_id, post_kp_id),
    CHECK (pre_kp_id != post_kp_id)
);

CREATE INDEX idx_prereq_post ON prerequisite_relation(post_kp_id);
CREATE INDEX idx_prereq_source ON prerequisite_relation(source);
```

### 3.3 题库管理模块

#### 3.3.1 questions - 题目主表
```sql
CREATE TABLE questions (
    question_id BIGSERIAL PRIMARY KEY,
    content JSONB NOT NULL,
    q_type SMALLINT NOT NULL CHECK (q_type BETWEEN 0 AND 10),
    difficulty_lvl SMALLINT CHECK (difficulty_lvl BETWEEN 1 AND 5),
    irt_ready BOOLEAN DEFAULT FALSE,
    answer_key JSONB,
    analysis TEXT,
    source TEXT,
    tags TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_questions_type ON questions(q_type);
CREATE INDEX idx_questions_difficulty ON questions(difficulty_lvl);
CREATE INDEX idx_questions_active ON questions(is_active);
CREATE INDEX idx_questions_irt ON questions(irt_ready);
CREATE INDEX idx_questions_content_gin ON questions USING GIN(to_tsvector('simple', content->>'stem'));
```

#### 3.3.2 question_assets - 题目媒资表
```sql
CREATE TABLE question_assets (
    asset_id BIGSERIAL PRIMARY KEY,
    question_id BIGINT REFERENCES questions(question_id) ON DELETE CASCADE,
    uri TEXT NOT NULL,
    media_type VARCHAR(32),
    file_size BIGINT,
    alt_text TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_assets_question ON question_assets(question_id);
```

#### 3.3.3 item_param - IRT项目参数表
```sql
CREATE TABLE item_param (
    question_id BIGINT PRIMARY KEY REFERENCES questions(question_id),
    a REAL, -- 区分度
    b REAL, -- 难度
    c REAL, -- 猜测率
    model_type VARCHAR(16) DEFAULT '3PL',
    calibration_sample_size INTEGER,
    last_calibrated TIMESTAMPTZ,
    calibration_quality NUMERIC(3,2)
);
```

### 3.4 关联映射模块

#### 3.4.1 item_kp_map - 题-知识点映射表（Q-矩阵）
```sql
CREATE TABLE item_kp_map (
    item_id BIGINT REFERENCES questions(question_id),
    kp_id INTEGER REFERENCES knowledge_points(kp_id),
    relation_type SMALLINT DEFAULT 0 CHECK (relation_type IN (0, 1)), -- 0=assess, 1=require
    confidence NUMERIC(3,2) CHECK (confidence BETWEEN 0 AND 1),
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (item_id, kp_id)
);

CREATE INDEX idx_item_kp_kp ON item_kp_map(kp_id);
CREATE INDEX idx_item_kp_confidence ON item_kp_map(confidence);
```

#### 3.4.2 question_relation - 题-题关系表
```sql
CREATE TYPE q_rel_type AS ENUM ('complements', 'progresses_to', 'equivalent', 'prerequisite', 'revision');

CREATE TABLE question_relation (
    src_q_id BIGINT REFERENCES questions(question_id),
    dst_q_id BIGINT REFERENCES questions(question_id),
    rel_type q_rel_type NOT NULL,
    confidence NUMERIC(3,2) CHECK (confidence BETWEEN 0 AND 1),
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (src_q_id, dst_q_id, rel_type),
    CHECK (src_q_id != dst_q_id)
);

CREATE INDEX idx_q_rel_dst ON question_relation(dst_q_id);
CREATE INDEX idx_q_rel_type ON question_relation(rel_type);
```

### 3.5 知识空间模块

#### 3.5.1 knowledge_state - 知识状态表
```sql
CREATE TABLE knowledge_state (
    state_id BIGSERIAL PRIMARY KEY,
    state_vector BIT VARYING NOT NULL,
    is_minimal BOOLEAN DEFAULT FALSE,
    is_maximal BOOLEAN DEFAULT FALSE,
    origin SMALLINT DEFAULT 0 CHECK (origin IN (0, 1, 2)), -- 0=algorithm, 1=observed, 2=expert
    frequency INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_state_vector ON knowledge_state USING BTREE(state_vector);
CREATE INDEX idx_state_minimal ON knowledge_state(is_minimal);
CREATE INDEX idx_state_maximal ON knowledge_state(is_maximal);
```

#### 3.5.2 state_transition - 状态转移表
```sql
CREATE TABLE state_transition (
    from_state BIGINT REFERENCES knowledge_state(state_id),
    to_state BIGINT REFERENCES knowledge_state(state_id),
    learned_kp INTEGER REFERENCES knowledge_points(kp_id),
    edge_weight REAL DEFAULT 1.0,
    transition_count INTEGER DEFAULT 0,
    PRIMARY KEY (from_state, to_state, learned_kp)
);

CREATE INDEX idx_transition_from ON state_transition(from_state);
CREATE INDEX idx_transition_kp ON state_transition(learned_kp);
```

### 3.6 标注工作流模块

#### 3.6.1 annotation_tasks - 标注任务表
```sql
CREATE TYPE task_state AS ENUM ('pending', 'in_progress', 'review', 'done', 'cancelled');

CREATE TABLE annotation_tasks (
    task_id BIGSERIAL PRIMARY KEY,
    task_type SMALLINT NOT NULL CHECK (task_type IN (0, 1, 2)), -- 0=Q-KP, 1=Q-Rel, 2=Prereq
    title TEXT NOT NULL,
    description TEXT,
    payload JSONB,
    assignees BIGINT[],
    reviewer_id BIGINT REFERENCES users(user_id),
    state task_state DEFAULT 'pending',
    priority SMALLINT DEFAULT 3 CHECK (priority BETWEEN 1 AND 5),
    due_date TIMESTAMPTZ,
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

CREATE INDEX idx_tasks_state ON annotation_tasks(state);
CREATE INDEX idx_tasks_assignees ON annotation_tasks USING GIN(assignees);
CREATE INDEX idx_tasks_type ON annotation_tasks(task_type);
```

#### 3.6.2 annotation_logs - 标注操作日志表
```sql
CREATE TABLE annotation_logs (
    log_id BIGSERIAL PRIMARY KEY,
    task_id BIGINT REFERENCES annotation_tasks(task_id),
    question_id BIGINT,
    operation VARCHAR(32) NOT NULL,
    detail JSONB,
    operator_id BIGINT REFERENCES users(user_id),
    ts TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_logs_task ON annotation_logs(task_id);
CREATE INDEX idx_logs_question ON annotation_logs(question_id);
CREATE INDEX idx_logs_operator ON annotation_logs(operator_id);
CREATE INDEX idx_logs_ts ON annotation_logs(ts);
```

### 3.7 版本管理模块

#### 3.7.1 question_versions - 题目版本表
```sql
CREATE TABLE question_versions (
    version_id BIGSERIAL PRIMARY KEY,
    question_id BIGINT REFERENCES questions(question_id),
    revision_num INTEGER NOT NULL,
    snapshot JSONB NOT NULL,
    changes_note TEXT,
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(question_id, revision_num)
);

CREATE INDEX idx_q_versions_question ON question_versions(question_id);
```

#### 3.7.2 kp_versions - 知识点版本表
```sql
CREATE TABLE kp_versions (
    version_id BIGSERIAL PRIMARY KEY,
    kp_id INTEGER REFERENCES knowledge_points(kp_id),
    revision_num INTEGER NOT NULL,
    snapshot JSONB NOT NULL,
    changes_note TEXT,
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(kp_id, revision_num)
);

CREATE INDEX idx_kp_versions_kp ON kp_versions(kp_id);
```

### 3.8 模型参数模块

#### 3.8.1 skill_param - BKT技能参数表
```sql
CREATE TABLE skill_param (
    kp_id INTEGER PRIMARY KEY REFERENCES knowledge_points(kp_id),
    p_l0 REAL CHECK (p_l0 BETWEEN 0 AND 1), -- 初始掌握概率
    p_t REAL CHECK (p_t BETWEEN 0 AND 1),   -- 学习率
    p_g REAL CHECK (p_g BETWEEN 0 AND 1),   -- 猜对概率
    p_s REAL CHECK (p_s BETWEEN 0 AND 1),   -- 滑落概率
    model_version VARCHAR(16),
    last_update TIMESTAMPTZ DEFAULT NOW(),
    calibration_quality NUMERIC(3,2)
);
```

## 4. 数据一致性约束

### 4.1 触发器函数

#### 4.1.1 检测先修关系环路
```sql
CREATE OR REPLACE FUNCTION check_prerequisite_cycle()
RETURNS TRIGGER AS $$
BEGIN
    -- 使用递归CTE检测环路
    WITH RECURSIVE cycle_check AS (
        SELECT NEW.pre_kp_id as start_kp, NEW.post_kp_id as current_kp, 1 as depth
        UNION ALL
        SELECT cc.start_kp, pr.post_kp_id, cc.depth + 1
        FROM cycle_check cc
        JOIN prerequisite_relation pr ON cc.current_kp = pr.pre_kp_id
        WHERE cc.depth < 100 -- 防止无限递归
    )
    SELECT 1 FROM cycle_check WHERE start_kp = current_kp LIMIT 1;
    
    IF FOUND THEN
        RAISE EXCEPTION '先修关系不能形成环路';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_check_prerequisite_cycle
    BEFORE INSERT OR UPDATE ON prerequisite_relation
    FOR EACH ROW EXECUTE FUNCTION check_prerequisite_cycle();
```

#### 4.1.2 更新知识点路径
```sql
CREATE OR REPLACE FUNCTION update_kp_path()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.parent_id IS NULL THEN
        NEW.path = NEW.kp_id::text::ltree;
    ELSE
        SELECT path || NEW.kp_id::text INTO NEW.path
        FROM knowledge_points WHERE kp_id = NEW.parent_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_kp_path
    BEFORE INSERT OR UPDATE ON knowledge_points
    FOR EACH ROW EXECUTE FUNCTION update_kp_path();
```

### 4.2 数据校验规则

#### 4.2.1 业务规则约束
- 每个题目至少关联一个知识点
- 知识点先修关系不能形成环路
- IRT参数的合理性检查
- 标注置信度阈值控制

#### 4.2.2 数据质量检查视图
```sql
CREATE VIEW v_data_quality_check AS
SELECT 
    'orphan_questions' as check_type,
    COUNT(*) as count,
    'Questions without knowledge points' as description
FROM questions q
LEFT JOIN item_kp_map ikm ON q.question_id = ikm.item_id
WHERE ikm.item_id IS NULL AND q.is_active = TRUE

UNION ALL

SELECT 
    'orphan_knowledge_points' as check_type,
    COUNT(*) as count,
    'Knowledge points without questions' as description
FROM knowledge_points kp
LEFT JOIN item_kp_map ikm ON kp.kp_id = ikm.kp_id
WHERE ikm.kp_id IS NULL AND kp.is_leaf = TRUE;
```

## 5. 性能优化策略

### 5.1 索引优化
- 复合索引支持多条件查询
- 部分索引减少存储开销
- 函数索引支持复杂查询

### 5.2 分区策略
- 按时间分区的日志表
- 按状态分区的任务表

### 5.3 缓存策略
- Redis缓存热点数据
- 物化视图缓存复杂查询结果

## 6. 备份与恢复

### 6.1 备份策略
- 每日全量备份
- 实时WAL归档
- 定期备份验证

### 6.2 恢复策略
- 点时间恢复
- 版本回滚机制
- 灾难恢复预案
