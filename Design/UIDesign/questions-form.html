<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目表单 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .form-container {
            max-width: 900px;
            margin: 0 auto;
        }
        
        .form-section {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .section-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-4);
            color: var(--gray-900);
            border-bottom: 2px solid var(--primary-500);
            padding-bottom: var(--space-2);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-4);
            margin-bottom: var(--space-4);
        }
        
        .form-row.full-width {
            grid-template-columns: 1fr;
        }
        
        .options-container {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            background: var(--gray-50);
        }
        
        .option-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-3);
            padding: var(--space-3);
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
        }
        
        .option-label {
            font-weight: var(--font-semibold);
            min-width: 30px;
        }
        
        .option-input {
            flex: 1;
        }
        
        .option-correct {
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }
        
        .add-option-btn {
            margin-top: var(--space-2);
        }
        
        .irt-parameters {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: var(--space-4);
        }
        
        .form-actions {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">首页</a>
                <a href="knowledge-space.html" class="nav-link active">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link">标注工作流</a>
                <a href="quality-control.html" class="nav-link">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item active">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">新建题目</h1>
                    <p class="text-gray-600">创建新的题目并设置相关属性</p>
                </div>
                <div>
                    <button class="btn btn-outline" onclick="history.back()">
                        ← 返回
                    </button>
                </div>
            </div>
        </div>

        <form id="question-form" class="form-container">
            <!-- 基本信息 -->
            <div class="form-section">
                <h2 class="section-title">基本信息</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="q_id">题目ID</label>
                        <input type="text" class="input" id="q_id" name="q_id" placeholder="系统自动分配" readonly>
                        <div class="help-text">系统自动分配的唯一标识符</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="q_type">题目类型 *</label>
                        <select class="select" id="q_type" name="q_type" required>
                            <option value="">选择题目类型</option>
                            <option value="0">单选题</option>
                            <option value="1">多选题</option>
                            <option value="2">判断题</option>
                            <option value="3">填空题</option>
                            <option value="4">简答题</option>
                            <option value="5">计算题</option>
                            <option value="6">证明题</option>
                            <option value="7">作文题</option>
                            <option value="8">听力题</option>
                            <option value="9">口语题</option>
                            <option value="10">实验题</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="difficulty_lvl">难度等级</label>
                        <select class="select" id="difficulty_lvl" name="difficulty_lvl">
                            <option value="">选择难度等级</option>
                            <option value="1">1级 - 基础</option>
                            <option value="2">2级 - 简单</option>
                            <option value="3">3级 - 中等</option>
                            <option value="4">4级 - 困难</option>
                            <option value="5">5级 - 极难</option>
                        </select>
                        <div class="help-text">题目的难度等级(1-5)</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="source">题目来源</label>
                        <input type="text" class="input" id="source" name="source" placeholder="如：教材练习、模拟考试等">
                        <div class="help-text">题目的来源信息</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="is_active">题目状态</label>
                        <select class="select" id="is_active" name="is_active">
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="label" for="created_by">创建人</label>
                        <input type="text" class="input" id="created_by" name="created_by" value="当前用户" readonly>
                        <div class="help-text">系统自动填充当前用户</div>
                    </div>
                </div>
            </div>

            <!-- 题目内容 -->
            <div class="form-section">
                <h2 class="section-title">题目内容</h2>
                
                <div class="form-row full-width">
                    <div class="form-group">
                        <label class="label" for="stem">题干 *</label>
                        <textarea class="textarea" id="stem" name="stem" rows="6" 
                                placeholder="请输入题目的题干内容..." required></textarea>
                        <div class="help-text">题目的主要内容，支持富文本格式</div>
                    </div>
                </div>

                <div class="form-row full-width">
                    <div class="form-group">
                        <label class="label">选项设置</label>
                        <div class="options-container" id="options-container">
                            <div class="option-item">
                                <span class="option-label">A.</span>
                                <input type="text" class="input option-input" name="option_a" placeholder="选项A内容">
                                <div class="option-correct">
                                    <input type="radio" name="correct_answer" value="A" id="correct_a">
                                    <label for="correct_a">正确</label>
                                </div>
                            </div>
                            <div class="option-item">
                                <span class="option-label">B.</span>
                                <input type="text" class="input option-input" name="option_b" placeholder="选项B内容">
                                <div class="option-correct">
                                    <input type="radio" name="correct_answer" value="B" id="correct_b">
                                    <label for="correct_b">正确</label>
                                </div>
                            </div>
                            <div class="option-item">
                                <span class="option-label">C.</span>
                                <input type="text" class="input option-input" name="option_c" placeholder="选项C内容">
                                <div class="option-correct">
                                    <input type="radio" name="correct_answer" value="C" id="correct_c">
                                    <label for="correct_c">正确</label>
                                </div>
                            </div>
                            <div class="option-item">
                                <span class="option-label">D.</span>
                                <input type="text" class="input option-input" name="option_d" placeholder="选项D内容">
                                <div class="option-correct">
                                    <input type="radio" name="correct_answer" value="D" id="correct_d">
                                    <label for="correct_d">正确</label>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline btn-sm add-option-btn" onclick="addOption()">
                            ➕ 添加选项
                        </button>
                        <div class="help-text">根据题目类型设置选项，单选题选择一个正确答案，多选题可选择多个</div>
                    </div>
                </div>

                <div class="form-row full-width">
                    <div class="form-group">
                        <label class="label" for="analysis">题目解析</label>
                        <textarea class="textarea" id="analysis" name="analysis" rows="4" 
                                placeholder="请输入题目的解析内容..."></textarea>
                        <div class="help-text">题目的详细解析和解题思路</div>
                    </div>
                </div>
            </div>

            <!-- IRT参数 -->
            <div class="form-section">
                <h2 class="section-title">IRT参数</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="is_irt_calibrated">IRT校准状态</label>
                        <select class="select" id="is_irt_calibrated" name="is_irt_calibrated">
                            <option value="false">未校准</option>
                            <option value="true">已校准</option>
                        </select>
                        <div class="help-text">题目是否已进行IRT参数校准</div>
                    </div>
                </div>

                <div class="irt-parameters">
                    <div class="form-group">
                        <label class="label" for="irt_a">区分度参数(a)</label>
                        <input type="number" class="input" id="irt_a" name="irt_a" 
                               step="0.01" min="0" max="5" placeholder="0.00">
                        <div class="help-text">IRT模型的区分度参数</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="irt_b">难度参数(b)</label>
                        <input type="number" class="input" id="irt_b" name="irt_b" 
                               step="0.01" min="-5" max="5" placeholder="0.00">
                        <div class="help-text">IRT模型的难度参数</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="irt_c">猜测参数(c)</label>
                        <input type="number" class="input" id="irt_c" name="irt_c" 
                               step="0.01" min="0" max="1" placeholder="0.00">
                        <div class="help-text">IRT模型的猜测参数</div>
                    </div>
                </div>
            </div>

            <!-- 表单操作 -->
            <div class="form-actions">
                <div>
                    <button type="button" class="btn btn-outline" onclick="saveDraft()">
                        💾 保存草稿
                    </button>
                </div>
                <div class="flex gap-2">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        🔄 重置
                    </button>
                    <button type="button" class="btn btn-outline" onclick="previewQuestion()">
                        👁️ 预览
                    </button>
                    <button type="submit" class="btn btn-primary">
                        ✅ 保存题目
                    </button>
                </div>
            </div>
        </form>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 题目表单相关功能
        let optionCount = 4;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
            
            // 检查是否为编辑模式
            const urlParams = Utils.getUrlParams();
            if (urlParams.id) {
                loadQuestion(urlParams.id);
                document.querySelector('h1').textContent = '编辑题目';
            }
        });

        // 初始化表单
        function initializeForm() {
            const form = document.getElementById('question-form');
            
            // 表单提交事件
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                submitForm();
            });

            // 题目类型变化事件
            document.getElementById('q_type').addEventListener('change', function() {
                updateOptionsDisplay(this.value);
            });
        }

        // 根据题目类型更新选项显示
        function updateOptionsDisplay(qType) {
            const optionsContainer = document.getElementById('options-container');
            const addOptionBtn = document.querySelector('.add-option-btn');
            
            if (qType === '2') { // 判断题
                optionsContainer.innerHTML = `
                    <div class="option-item">
                        <span class="option-label">A.</span>
                        <input type="text" class="input option-input" name="option_a" value="正确" readonly>
                        <div class="option-correct">
                            <input type="radio" name="correct_answer" value="A" id="correct_a">
                            <label for="correct_a">正确</label>
                        </div>
                    </div>
                    <div class="option-item">
                        <span class="option-label">B.</span>
                        <input type="text" class="input option-input" name="option_b" value="错误" readonly>
                        <div class="option-correct">
                            <input type="radio" name="correct_answer" value="B" id="correct_b">
                            <label for="correct_b">正确</label>
                        </div>
                    </div>
                `;
                addOptionBtn.style.display = 'none';
            } else if (qType === '3' || qType === '4') { // 填空题或简答题
                optionsContainer.innerHTML = `
                    <div class="form-group">
                        <label class="label">参考答案</label>
                        <textarea class="textarea" name="reference_answer" rows="3" 
                                placeholder="请输入参考答案..."></textarea>
                    </div>
                `;
                addOptionBtn.style.display = 'none';
            } else {
                // 重置为选择题格式
                resetOptionsToDefault();
                addOptionBtn.style.display = 'inline-flex';
            }
        }

        // 重置选项为默认格式
        function resetOptionsToDefault() {
            const optionsContainer = document.getElementById('options-container');
            optionsContainer.innerHTML = `
                <div class="option-item">
                    <span class="option-label">A.</span>
                    <input type="text" class="input option-input" name="option_a" placeholder="选项A内容">
                    <div class="option-correct">
                        <input type="radio" name="correct_answer" value="A" id="correct_a">
                        <label for="correct_a">正确</label>
                    </div>
                </div>
                <div class="option-item">
                    <span class="option-label">B.</span>
                    <input type="text" class="input option-input" name="option_b" placeholder="选项B内容">
                    <div class="option-correct">
                        <input type="radio" name="correct_answer" value="B" id="correct_b">
                        <label for="correct_b">正确</label>
                    </div>
                </div>
                <div class="option-item">
                    <span class="option-label">C.</span>
                    <input type="text" class="input option-input" name="option_c" placeholder="选项C内容">
                    <div class="option-correct">
                        <input type="radio" name="correct_answer" value="C" id="correct_c">
                        <label for="correct_c">正确</label>
                    </div>
                </div>
                <div class="option-item">
                    <span class="option-label">D.</span>
                    <input type="text" class="input option-input" name="option_d" placeholder="选项D内容">
                    <div class="option-correct">
                        <input type="radio" name="correct_answer" value="D" id="correct_d">
                        <label for="correct_d">正确</label>
                    </div>
                </div>
            `;
            optionCount = 4;
        }

        // 添加选项
        function addOption() {
            if (optionCount >= 8) {
                Message.warning('最多只能添加8个选项');
                return;
            }
            
            optionCount++;
            const optionLabel = String.fromCharCode(64 + optionCount); // A, B, C, D, E, F, G, H
            
            const optionItem = document.createElement('div');
            optionItem.className = 'option-item';
            optionItem.innerHTML = `
                <span class="option-label">${optionLabel}.</span>
                <input type="text" class="input option-input" name="option_${optionLabel.toLowerCase()}" placeholder="选项${optionLabel}内容">
                <div class="option-correct">
                    <input type="radio" name="correct_answer" value="${optionLabel}" id="correct_${optionLabel.toLowerCase()}">
                    <label for="correct_${optionLabel.toLowerCase()}">正确</label>
                </div>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeOption(this)">×</button>
            `;
            
            document.getElementById('options-container').appendChild(optionItem);
        }

        // 移除选项
        function removeOption(button) {
            if (optionCount <= 2) {
                Message.warning('至少需要保留2个选项');
                return;
            }
            
            button.parentElement.remove();
            optionCount--;
        }

        // 提交表单
        function submitForm() {
            const formData = new FormData(document.getElementById('question-form'));
            const data = Object.fromEntries(formData.entries());
            
            // 处理选项数据
            const options = {};
            const correctAnswers = [];
            
            for (let [key, value] of formData.entries()) {
                if (key.startsWith('option_')) {
                    options[key] = value;
                }
                if (key === 'correct_answer') {
                    correctAnswers.push(value);
                }
            }
            
            data.options = options;
            data.correct_answer = correctAnswers.join(',');
            
            console.log('提交数据:', data);

            // 模拟API调用
            Message.info('正在保存题目...');
            
            setTimeout(() => {
                Message.success('题目保存成功！');
                setTimeout(() => {
                    location.href = 'questions.html';
                }, 1000);
            }, 2000);
        }

        // 保存草稿
        function saveDraft() {
            Message.success('草稿保存成功！');
        }

        // 重置表单
        function resetForm() {
            Modal.confirm(
                '确认重置',
                '确定要重置表单吗？所有未保存的更改将丢失。',
                () => {
                    document.getElementById('question-form').reset();
                    resetOptionsToDefault();
                    Message.info('表单已重置');
                }
            );
        }

        // 预览题目
        function previewQuestion() {
            const formData = new FormData(document.getElementById('question-form'));
            const data = Object.fromEntries(formData.entries());
            
            const previewContent = `
                <div class="question-preview">
                    <div class="question-content">
                        <div class="question-stem">${data.stem || '未填写题干'}</div>
                        <div class="question-options">
                            ${data.option_a ? `<div class="question-option">A. ${data.option_a}</div>` : ''}
                            ${data.option_b ? `<div class="question-option">B. ${data.option_b}</div>` : ''}
                            ${data.option_c ? `<div class="question-option">C. ${data.option_c}</div>` : ''}
                            ${data.option_d ? `<div class="question-option">D. ${data.option_d}</div>` : ''}
                        </div>
                    </div>
                    <div class="mt-4">
                        <strong>题目类型：</strong>${getQuestionTypeName(data.q_type)}
                    </div>
                    <div class="mt-2">
                        <strong>正确答案：</strong>${data.correct_answer || '未设置'}
                    </div>
                    ${data.analysis ? `<div class="mt-2"><strong>解析：</strong>${data.analysis}</div>` : ''}
                </div>
            `;

            Modal.show('题目预览', previewContent);
        }

        // 获取题目类型名称
        function getQuestionTypeName(type) {
            const types = {
                '0': '单选题', '1': '多选题', '2': '判断题', '3': '填空题', '4': '简答题',
                '5': '计算题', '6': '证明题', '7': '作文题', '8': '听力题', '9': '口语题', '10': '实验题'
            };
            return types[type] || '未选择';
        }

        // 加载题目数据（编辑模式）
        function loadQuestion(id) {
            // 模拟加载数据
            const mockData = {
                q_id: id,
                q_type: '0',
                stem: '解方程：2x + 3 = 7，x的值是多少？',
                option_a: 'x = 1',
                option_b: 'x = 2',
                option_c: 'x = 3',
                option_d: 'x = 4',
                correct_answer: 'B',
                analysis: '移项得：2x = 7 - 3 = 4，所以 x = 2',
                difficulty_lvl: '2',
                source: '教材练习',
                is_active: 'true',
                is_irt_calibrated: 'true',
                irt_a: '1.2',
                irt_b: '0.5',
                irt_c: '0.2'
            };

            // 填充表单
            Object.keys(mockData).forEach(key => {
                const field = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
                if (field) {
                    field.value = mockData[key];
                }
            });

            // 设置正确答案
            const correctRadio = document.querySelector(`input[name="correct_answer"][value="${mockData.correct_answer}"]`);
            if (correctRadio) {
                correctRadio.checked = true;
            }
        }
    </script>
</body>
</html>
