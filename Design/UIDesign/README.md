# 自适应学习服务数据标注模块 - UI设计参考

## 📋 项目概述

本目录包含了自适应学习服务数据标注模块的完整UI设计参考文件，基于现代化的Web设计标准，采用响应式布局，支持多设备访问。

## 🎨 设计规范

### 技术栈
- **前端框架**: React + Radix UI + Tailwind CSS
- **设计系统**: 基于现代化IT系统设计风格
- **响应式设计**: 支持桌面端、平板端、移动端
- **可访问性**: 符合WCAG 2.1标准

### 设计原则
- **用户中心**: 以用户体验为核心，简化操作流程
- **一致性**: 保持界面元素和交互行为的一致性
- **可访问性**: 支持无障碍访问
- **响应式**: 适配不同屏幕尺寸和设备
- **效率优先**: 提高标注工作效率，减少认知负担

## 📁 文件结构

```
UIDesign/
├── assets/
│   ├── css/
│   │   └── common.css              # 公共样式文件
│   ├── js/
│   │   └── common.js               # 公共JavaScript文件
│   └── images/                     # 图片资源目录
├── index.html                      # 系统首页
├── knowledge-points.html           # 知识点管理页面
├── knowledge-points-form.html      # 知识点表单页面
├── questions.html                  # 题目管理页面
├── questions-form.html             # 题目表单页面
├── annotation-tasks.html           # 标注任务管理页面
├── annotation-tasks-form.html      # 标注任务表单页面
├── quality-reports.html            # 质量报告页面
├── navigation.html                 # 导航索引页面
└── README.md                       # 本文件
```

## 🌟 页面功能说明

### 1. 系统首页 (index.html)
- **功能**: 系统概览和快速操作入口
- **特色**: 
  - 统计卡片展示关键指标
  - 快速操作面板
  - 最近活动时间线
  - 响应式仪表板布局

### 2. 知识点管理 (knowledge-points.html)
- **功能**: 知识点的CRUD操作和层级管理
- **特色**:
  - 树形结构展示知识点层级
  - 实时搜索和多维度筛选
  - 知识点详情面板
  - 先修关系可视化

### 3. 知识点表单 (knowledge-points-form.html)
- **功能**: 新建/编辑知识点
- **特色**:
  - 分步骤表单设计
  - 实时验证和错误提示
  - 先修关系选择器
  - 表单预览功能

### 4. 题目管理 (questions.html)
- **功能**: 题目库管理和批量操作
- **特色**:
  - 题目预览卡片
  - 批量选择和操作
  - 多条件筛选
  - IRT参数显示

### 5. 题目表单 (questions-form.html)
- **功能**: 新建/编辑题目
- **特色**:
  - 多种题型支持
  - 动态选项管理
  - IRT参数设置
  - 题目预览功能

### 6. 标注任务管理 (annotation-tasks.html)
- **功能**: 标注工作流管理
- **特色**:
  - 看板式任务管理
  - 任务状态可视化
  - 进度跟踪
  - 多视图切换

### 7. 标注任务表单 (annotation-tasks-form.html)
- **功能**: 创建/编辑标注任务
- **特色**:
  - 数据范围选择
  - 标注配置设置
  - 自动分派功能
  - 任务预览

### 8. 质量报告 (quality-reports.html)
- **功能**: 质量监控和分析
- **特色**:
  - 质量指标仪表板
  - 趋势分析图表
  - 问题列表和处理
  - 标注员表现分析

### 9. 导航页面 (navigation.html)
- **功能**: 所有页面的导航入口
- **特色**:
  - 页面功能介绍
  - 技术栈说明
  - 统计信息展示
  - 快速访问入口

## 🎯 核心功能特性

### CRUD操作
- ✅ **Create**: 新建知识点、题目、任务等
- ✅ **Read**: 列表查看、详情展示、搜索筛选
- ✅ **Update**: 编辑修改、状态更新
- ✅ **Delete**: 删除操作（含确认机制）

### 搜索和筛选
- 🔍 **模糊搜索**: 支持关键词模糊匹配
- 🏷️ **多维筛选**: 按类型、状态、时间等筛选
- 📊 **实时筛选**: 输入即时响应
- 🔄 **筛选重置**: 一键清除筛选条件

### 表单功能
- 📝 **智能表单**: 自动补全、联想输入
- ✅ **实时验证**: 字段级实时验证
- 💾 **草稿保存**: 自动保存和手动保存
- 👁️ **预览功能**: 提交前预览

### 批量操作
- ☑️ **批量选择**: 全选、反选、条件选择
- 🔧 **批量处理**: 批量编辑、删除、导出
- 📊 **操作反馈**: 进度显示和结果提示

## 🎨 UI组件库

### 基础组件
- **按钮**: Primary、Secondary、Outline、Danger等样式
- **表单**: Input、Select、Textarea、Checkbox、Radio
- **卡片**: 信息展示卡片，支持头部、内容、底部
- **表格**: 数据表格，支持排序、分页、筛选
- **模态框**: 对话框、确认框、表单弹窗

### 高级组件
- **数据表格**: 支持搜索、排序、分页的数据表格
- **树形组件**: 层级数据展示和操作
- **进度条**: 任务进度、质量得分展示
- **统计卡片**: 数据指标展示
- **时间线**: 活动记录展示

### 反馈组件
- **消息提示**: Success、Error、Warning、Info
- **加载状态**: 骨架屏、加载动画
- **空状态**: 无数据时的友好提示
- **错误状态**: 错误信息和解决建议

## 📱 响应式设计

### 断点设置
- **移动端**: < 768px
- **平板端**: 768px - 1023px  
- **桌面端**: ≥ 1024px

### 适配策略
- **移动端**: 单栏布局，大按钮设计，简化操作
- **平板端**: 两栏布局，触摸优化
- **桌面端**: 多栏布局，丰富交互

## 🔧 使用说明

### 本地预览
1. 直接在浏览器中打开HTML文件
2. 推荐使用现代浏览器（Chrome、Firefox、Safari、Edge）
3. 确保JavaScript已启用

### 开发集成
1. 复制`assets/css/common.css`作为基础样式
2. 复制`assets/js/common.js`作为工具函数库
3. 参考HTML结构进行组件开发
4. 使用设计规范中的颜色、字体、间距变量

### 自定义修改
1. 修改CSS变量调整主题色彩
2. 扩展JavaScript工具函数
3. 添加新的组件样式
4. 调整响应式断点

## 🎯 最佳实践

### 性能优化
- 使用CSS变量统一管理样式
- 图片懒加载和压缩
- JavaScript代码分割
- 缓存策略优化

### 可访问性
- 语义化HTML标签
- 键盘导航支持
- 屏幕阅读器兼容
- 色彩对比度符合标准

### 用户体验
- 加载状态提示
- 操作反馈及时
- 错误信息友好
- 操作流程简化

## 🗃️ 数据库字段匹配

所有表单字段和信息展示都严格按照数据库设计文档进行匹配：

### 知识点相关字段
- `kp_id`: 知识点ID (自增主键)
- `name`: 知识点名称
- `code`: 知识点编码
- `parent_id`: 父级知识点ID
- `path`: LTREE格式的层级路径
- `difficulty_level`: 难度等级(1-5)
- `is_leaf`: 是否叶子节点
- `description`: 知识点描述
- `created_by`: 创建人
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 题目相关字段
- `q_id`: 题目ID (自增主键)
- `q_type`: 题目类型(0-10)
- `stem`: 题干内容
- `options`: 选项内容(JSONB)
- `correct_answer`: 正确答案
- `analysis`: 题目解析
- `difficulty_lvl`: 难度等级(1-5)
- `source`: 题目来源
- `is_active`: 是否启用
- `is_irt_calibrated`: IRT是否校准
- `irt_a`, `irt_b`, `irt_c`: IRT参数
- `created_by`: 创建人
- `created_at`: 创建时间

### 标注任务相关字段
- `task_id`: 任务ID (自增主键)
- `task_type`: 任务类型(0-2)
- `title`: 任务标题
- `description`: 任务描述
- `status`: 任务状态(0-6)
- `priority`: 优先级(1-4)
- `assigned_to`: 分派给
- `created_by`: 创建人
- `deadline`: 截止时间
- `data_source`: 数据来源
- `annotation_schema`: 标注模式
- `quality_threshold`: 质量阈值

## 📞 技术支持

如需技术支持或有改进建议，请联系开发团队。

---

**注意**: 这些HTML文件严格按照数据库设计文档进行字段匹配，可直接作为前端开发的参考模板。实际开发时需要根据具体的技术栈进行适配和集成。
