<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目管理 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .search-filters {
            background: white;
            padding: var(--space-6);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--space-6);
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            align-items: end;
        }
        
        .question-preview {
            max-width: 600px;
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            margin: var(--space-2) 0;
        }
        
        .question-content {
            margin-bottom: var(--space-3);
        }
        
        .question-stem {
            font-weight: var(--font-medium);
            margin-bottom: var(--space-2);
            line-height: 1.6;
        }
        
        .question-options {
            margin-left: var(--space-4);
        }
        
        .question-option {
            margin-bottom: var(--space-1);
            padding: var(--space-1) 0;
        }
        
        .question-meta {
            display: flex;
            gap: var(--space-4);
            font-size: var(--text-sm);
            color: var(--gray-600);
            border-top: 1px solid var(--gray-200);
            padding-top: var(--space-2);
        }
        
        .knowledge-points {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-1);
            margin-top: var(--space-2);
        }
        
        .knowledge-point-tag {
            background: var(--primary-100);
            color: var(--primary-800);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
            font-size: var(--text-xs);
        }
        
        .question-actions {
            display: flex;
            gap: var(--space-1);
            align-items: center;
        }
        
        .batch-actions {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            display: none;
        }
        
        .batch-actions.show {
            display: block;
        }
        
        .stats-bar {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--space-4);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--text-xl);
            font-weight: var(--font-bold);
            color: var(--primary-600);
        }
        
        .stat-label {
            font-size: var(--text-sm);
            color: var(--gray-600);
            margin-top: var(--space-1);
        }
        
        .question-row {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            transition: all 0.15s ease;
        }
        
        .question-row:hover {
            box-shadow: var(--shadow-md);
        }
        
        .question-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-3);
        }
        
        .question-id {
            font-weight: var(--font-semibold);
            color: var(--gray-700);
        }
        
        .question-type {
            background: var(--secondary-100);
            color: var(--secondary-700);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
            font-size: var(--text-xs);
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">首页</a>
                <a href="knowledge-space.html" class="nav-link active">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link">标注工作流</a>
                <a href="quality-control.html" class="nav-link">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item active">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">题目管理</h1>
                    <p class="text-gray-600">管理题目库和题目-知识点关联关系</p>
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-outline" onclick="exportQuestions()">
                        📤 导出题目
                    </button>
                    <button class="btn btn-outline" onclick="importQuestions()">
                        📥 导入题目
                    </button>
                    <button class="btn btn-primary" onclick="location.href='questions-form.html'">
                        ➕ 新建题目
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number">5,678</div>
                <div class="stat-label">题目总数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">4,521</div>
                <div class="stat-label">已标注</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">1,157</div>
                <div class="stat-label">待标注</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">89.2%</div>
                <div class="stat-label">标注完成率</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">95.6%</div>
                <div class="stat-label">质量得分</div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filters">
            <div class="filter-row">
                <div class="form-group">
                    <label class="label">搜索题目</label>
                    <input type="text" class="input" placeholder="输入题目内容或ID" id="search-input">
                </div>
                <div class="form-group">
                    <label class="label">题目类型</label>
                    <select class="select" id="type-filter">
                        <option value="">全部类型</option>
                        <option value="0">单选题</option>
                        <option value="1">多选题</option>
                        <option value="2">判断题</option>
                        <option value="3">填空题</option>
                        <option value="4">简答题</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="label">难度等级</label>
                    <select class="select" id="difficulty-filter">
                        <option value="">全部难度</option>
                        <option value="1">1级 - 基础</option>
                        <option value="2">2级 - 简单</option>
                        <option value="3">3级 - 中等</option>
                        <option value="4">4级 - 困难</option>
                        <option value="5">5级 - 极难</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="label">标注状态</label>
                    <select class="select" id="annotation-filter">
                        <option value="">全部状态</option>
                        <option value="annotated">已标注</option>
                        <option value="pending">待标注</option>
                        <option value="reviewing">审核中</option>
                        <option value="approved">已审核</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="label">知识点</label>
                    <select class="select" id="knowledge-point-filter">
                        <option value="">全部知识点</option>
                        <option value="1">一元一次方程</option>
                        <option value="2">一元二次方程</option>
                        <option value="3">函数概念</option>
                        <option value="4">英语语法</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="applyFilters()">搜索</button>
                    <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                </div>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-actions" id="batch-actions">
            <div class="flex items-center justify-between">
                <div>
                    已选择 <span id="selected-count">0</span> 个题目
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-outline btn-sm" onclick="batchAnnotate()">
                        ✏️ 批量标注
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="batchExport()">
                        📤 批量导出
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="batchDelete()">
                        🗑️ 批量删除
                    </button>
                </div>
            </div>
        </div>

        <!-- 题目列表 -->
        <div id="questions-container">
            <!-- 题目1 -->
            <div class="question-row">
                <div class="question-header">
                    <div class="flex items-center gap-3">
                        <input type="checkbox" class="question-checkbox" data-id="1001">
                        <span class="question-id">Q1001</span>
                        <span class="question-type">单选题</span>
                        <span class="badge badge-success">已标注</span>
                    </div>
                    <div class="question-actions">
                        <button class="btn btn-sm btn-outline" onclick="editQuestion(1001)">✏️ 编辑</button>
                        <button class="btn btn-sm btn-outline" onclick="annotateQuestion(1001)">🏷️ 标注</button>
                        <button class="btn btn-sm btn-outline" onclick="previewQuestion(1001)">👁️ 预览</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteQuestion(1001)">🗑️</button>
                    </div>
                </div>
                
                <div class="question-preview">
                    <div class="question-content">
                        <div class="question-stem">
                            解方程：2x + 3 = 7，x的值是多少？
                        </div>
                        <div class="question-options">
                            <div class="question-option">A. x = 1</div>
                            <div class="question-option">B. x = 2</div>
                            <div class="question-option">C. x = 3</div>
                            <div class="question-option">D. x = 4</div>
                        </div>
                    </div>
                    
                    <div class="question-meta">
                        <span>难度: 2级</span>
                        <span>IRT参数: a=1.2, b=0.5</span>
                        <span>创建时间: 2024-01-15</span>
                        <span>创建人: 张三</span>
                    </div>
                    
                    <div class="knowledge-points">
                        <span class="knowledge-point-tag">一元一次方程</span>
                        <span class="knowledge-point-tag">代数运算</span>
                    </div>
                </div>
            </div>

            <!-- 题目2 -->
            <div class="question-row">
                <div class="question-header">
                    <div class="flex items-center gap-3">
                        <input type="checkbox" class="question-checkbox" data-id="1002">
                        <span class="question-id">Q1002</span>
                        <span class="question-type">多选题</span>
                        <span class="badge badge-warning">待标注</span>
                    </div>
                    <div class="question-actions">
                        <button class="btn btn-sm btn-outline" onclick="editQuestion(1002)">✏️ 编辑</button>
                        <button class="btn btn-sm btn-primary" onclick="annotateQuestion(1002)">🏷️ 标注</button>
                        <button class="btn btn-sm btn-outline" onclick="previewQuestion(1002)">👁️ 预览</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteQuestion(1002)">🗑️</button>
                    </div>
                </div>
                
                <div class="question-preview">
                    <div class="question-content">
                        <div class="question-stem">
                            下列哪些是一元二次方程的解法？（多选）
                        </div>
                        <div class="question-options">
                            <div class="question-option">A. 因式分解法</div>
                            <div class="question-option">B. 配方法</div>
                            <div class="question-option">C. 公式法</div>
                            <div class="question-option">D. 消元法</div>
                        </div>
                    </div>
                    
                    <div class="question-meta">
                        <span>难度: 3级</span>
                        <span>IRT参数: 未校准</span>
                        <span>创建时间: 2024-01-16</span>
                        <span>创建人: 李四</span>
                    </div>
                    
                    <div class="knowledge-points">
                        <span class="knowledge-point-tag">待标注</span>
                    </div>
                </div>
            </div>

            <!-- 题目3 -->
            <div class="question-row">
                <div class="question-header">
                    <div class="flex items-center gap-3">
                        <input type="checkbox" class="question-checkbox" data-id="1003">
                        <span class="question-id">Q1003</span>
                        <span class="question-type">填空题</span>
                        <span class="badge badge-info">审核中</span>
                    </div>
                    <div class="question-actions">
                        <button class="btn btn-sm btn-outline" onclick="editQuestion(1003)">✏️ 编辑</button>
                        <button class="btn btn-sm btn-outline" onclick="annotateQuestion(1003)">🏷️ 标注</button>
                        <button class="btn btn-sm btn-outline" onclick="previewQuestion(1003)">👁️ 预览</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteQuestion(1003)">🗑️</button>
                    </div>
                </div>
                
                <div class="question-preview">
                    <div class="question-content">
                        <div class="question-stem">
                            函数 f(x) = 2x + 1 在 x = 3 时的函数值是 ______。
                        </div>
                    </div>
                    
                    <div class="question-meta">
                        <span>难度: 2级</span>
                        <span>IRT参数: a=0.9, b=0.3</span>
                        <span>创建时间: 2024-01-17</span>
                        <span>创建人: 王五</span>
                    </div>
                    
                    <div class="knowledge-points">
                        <span class="knowledge-point-tag">函数概念</span>
                        <span class="knowledge-point-tag">函数值计算</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination">
            <a href="#" class="pagination-item">上一页</a>
            <a href="#" class="pagination-item active">1</a>
            <a href="#" class="pagination-item">2</a>
            <a href="#" class="pagination-item">3</a>
            <a href="#" class="pagination-item">4</a>
            <a href="#" class="pagination-item">5</a>
            <a href="#" class="pagination-item">下一页</a>
        </div>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 题目管理相关功能
        let selectedQuestions = new Set();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeQuestionList();
            updateBatchActions();
        });

        // 初始化题目列表
        function initializeQuestionList() {
            // 绑定复选框事件
            const checkboxes = document.querySelectorAll('.question-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const questionId = this.dataset.id;
                    if (this.checked) {
                        selectedQuestions.add(questionId);
                    } else {
                        selectedQuestions.delete(questionId);
                    }
                    updateBatchActions();
                });
            });

            // 全选功能
            const selectAllBtn = document.createElement('button');
            selectAllBtn.className = 'btn btn-outline btn-sm';
            selectAllBtn.textContent = '全选';
            selectAllBtn.onclick = selectAllQuestions;
            
            const clearSelectionBtn = document.createElement('button');
            clearSelectionBtn.className = 'btn btn-outline btn-sm';
            clearSelectionBtn.textContent = '清除选择';
            clearSelectionBtn.onclick = clearSelection;
            
            // 添加到搜索筛选区域
            const filterRow = document.querySelector('.filter-row');
            const buttonGroup = document.createElement('div');
            buttonGroup.className = 'form-group flex gap-2';
            buttonGroup.appendChild(selectAllBtn);
            buttonGroup.appendChild(clearSelectionBtn);
            filterRow.appendChild(buttonGroup);
        }

        // 更新批量操作显示
        function updateBatchActions() {
            const batchActions = document.getElementById('batch-actions');
            const selectedCount = document.getElementById('selected-count');
            
            selectedCount.textContent = selectedQuestions.size;
            
            if (selectedQuestions.size > 0) {
                batchActions.classList.add('show');
            } else {
                batchActions.classList.remove('show');
            }
        }

        // 全选题目
        function selectAllQuestions() {
            const checkboxes = document.querySelectorAll('.question-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                selectedQuestions.add(checkbox.dataset.id);
            });
            updateBatchActions();
        }

        // 清除选择
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.question-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectedQuestions.clear();
            updateBatchActions();
        }

        // 应用筛选条件
        function applyFilters() {
            const searchTerm = document.getElementById('search-input').value;
            const type = document.getElementById('type-filter').value;
            const difficulty = document.getElementById('difficulty-filter').value;
            const annotation = document.getElementById('annotation-filter').value;
            const knowledgePoint = document.getElementById('knowledge-point-filter').value;
            
            console.log('应用筛选:', { searchTerm, type, difficulty, annotation, knowledgePoint });
            
            // 这里实现筛选逻辑
            Message.info('筛选条件已应用');
        }

        // 重置筛选条件
        function resetFilters() {
            document.getElementById('search-input').value = '';
            document.getElementById('type-filter').value = '';
            document.getElementById('difficulty-filter').value = '';
            document.getElementById('annotation-filter').value = '';
            document.getElementById('knowledge-point-filter').value = '';
            
            applyFilters();
        }

        // 编辑题目
        function editQuestion(id) {
            location.href = `questions-form.html?id=${id}`;
        }

        // 标注题目
        function annotateQuestion(id) {
            location.href = `question-annotation.html?id=${id}`;
        }

        // 预览题目
        function previewQuestion(id) {
            // 模拟题目数据
            const questionData = {
                id: id,
                stem: '解方程：2x + 3 = 7，x的值是多少？',
                options: ['A. x = 1', 'B. x = 2', 'C. x = 3', 'D. x = 4'],
                answer: 'B',
                analysis: '移项得：2x = 7 - 3 = 4，所以 x = 2'
            };

            const previewContent = `
                <div class="question-preview">
                    <div class="question-content">
                        <div class="question-stem">${questionData.stem}</div>
                        <div class="question-options">
                            ${questionData.options.map(option => `<div class="question-option">${option}</div>`).join('')}
                        </div>
                    </div>
                    <div class="mt-4">
                        <strong>正确答案：</strong>${questionData.answer}
                    </div>
                    <div class="mt-2">
                        <strong>解析：</strong>${questionData.analysis}
                    </div>
                </div>
            `;

            Modal.show(`题目预览 - Q${id}`, previewContent);
        }

        // 删除题目
        function deleteQuestion(id) {
            Modal.confirm(
                '确认删除',
                `确定要删除题目 Q${id} 吗？删除后将无法恢复。`,
                () => {
                    console.log('删除题目:', id);
                    Message.success('题目删除成功');
                    // 这里可以刷新列表
                }
            );
        }

        // 批量标注
        function batchAnnotate() {
            if (selectedQuestions.size === 0) {
                Message.warning('请先选择要标注的题目');
                return;
            }
            
            const questionIds = Array.from(selectedQuestions);
            location.href = `batch-annotation.html?ids=${questionIds.join(',')}`;
        }

        // 批量导出
        function batchExport() {
            if (selectedQuestions.size === 0) {
                Message.warning('请先选择要导出的题目');
                return;
            }
            
            Message.info('正在导出选中的题目...');
            setTimeout(() => {
                Message.success(`成功导出 ${selectedQuestions.size} 个题目`);
            }, 2000);
        }

        // 批量删除
        function batchDelete() {
            if (selectedQuestions.size === 0) {
                Message.warning('请先选择要删除的题目');
                return;
            }
            
            Modal.confirm(
                '确认批量删除',
                `确定要删除选中的 ${selectedQuestions.size} 个题目吗？删除后将无法恢复。`,
                () => {
                    console.log('批量删除题目:', Array.from(selectedQuestions));
                    Message.success(`成功删除 ${selectedQuestions.size} 个题目`);
                    selectedQuestions.clear();
                    updateBatchActions();
                    // 这里可以刷新列表
                }
            );
        }

        // 导出题目
        function exportQuestions() {
            Message.info('正在导出题目数据...');
            setTimeout(() => {
                Message.success('题目数据导出成功');
            }, 2000);
        }

        // 导入题目
        function importQuestions() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls,.csv';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    Message.info('正在导入题目数据...');
                    setTimeout(() => {
                        Message.success('题目数据导入成功');
                    }, 2000);
                }
            };
            input.click();
        }
    </script>
</body>
</html>
