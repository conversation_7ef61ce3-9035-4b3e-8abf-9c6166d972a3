<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标注任务表单 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .form-section {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .section-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-4);
            color: var(--gray-900);
            border-bottom: 2px solid var(--primary-500);
            padding-bottom: var(--space-2);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-4);
            margin-bottom: var(--space-4);
        }
        
        .form-row.full-width {
            grid-template-columns: 1fr;
        }
        
        .data-selector {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            background: var(--gray-50);
            max-height: 300px;
            overflow-y: auto;
        }
        
        .data-item {
            padding: var(--space-2);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-sm);
            margin-bottom: var(--space-2);
            background: white;
            cursor: pointer;
            transition: all 0.15s ease;
        }
        
        .data-item:hover {
            background: var(--primary-50);
        }
        
        .data-item.selected {
            background: var(--primary-100);
            border-color: var(--primary-500);
        }
        
        .selected-count {
            font-size: var(--text-sm);
            color: var(--primary-600);
            margin-top: var(--space-2);
        }
        
        .form-actions {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">首页</a>
                <a href="knowledge-space.html" class="nav-link">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link active">标注工作流</a>
                <a href="quality-control.html" class="nav-link">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item active">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">创建标注任务</h1>
                    <p class="text-gray-600">创建新的标注任务并分派给标注员</p>
                </div>
                <div>
                    <button class="btn btn-outline" onclick="history.back()">
                        ← 返回
                    </button>
                </div>
            </div>
        </div>

        <form id="task-form" class="form-container">
            <!-- 基本信息 -->
            <div class="form-section">
                <h2 class="section-title">基本信息</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="task_id">任务ID</label>
                        <input type="text" class="input" id="task_id" name="task_id" placeholder="系统自动分配" readonly>
                        <div class="help-text">系统自动分配的唯一标识符</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="task_type">任务类型 *</label>
                        <select class="select" id="task_type" name="task_type" required>
                            <option value="">选择任务类型</option>
                            <option value="0">题-知识点标注</option>
                            <option value="1">题-题关系标注</option>
                            <option value="2">先修关系标注</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="title">任务标题 *</label>
                        <input type="text" class="input" id="title" name="title" placeholder="请输入任务标题" required>
                        <div class="help-text">简洁明确的任务标题</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="priority">优先级</label>
                        <select class="select" id="priority" name="priority">
                            <option value="1">低</option>
                            <option value="2" selected>中</option>
                            <option value="3">高</option>
                            <option value="4">紧急</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="assigned_to">分派给</label>
                        <select class="select" id="assigned_to" name="assigned_to">
                            <option value="">选择标注员</option>
                            <option value="user_1001">张三</option>
                            <option value="user_1002">李四</option>
                            <option value="user_1003">王五</option>
                            <option value="user_1004">赵六</option>
                        </select>
                        <div class="help-text">选择负责此任务的标注员</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="deadline">截止时间</label>
                        <input type="datetime-local" class="input" id="deadline" name="deadline">
                        <div class="help-text">任务完成的截止时间</div>
                    </div>
                </div>

                <div class="form-row full-width">
                    <div class="form-group">
                        <label class="label" for="description">任务描述</label>
                        <textarea class="textarea" id="description" name="description" rows="4" 
                                placeholder="请详细描述任务要求和标注标准..."></textarea>
                        <div class="help-text">详细的任务描述和标注要求</div>
                    </div>
                </div>
            </div>

            <!-- 数据范围 -->
            <div class="form-section">
                <h2 class="section-title">数据范围</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="data_source">数据来源</label>
                        <select class="select" id="data_source" name="data_source">
                            <option value="questions">题目库</option>
                            <option value="knowledge_points">知识点库</option>
                            <option value="custom">自定义数据</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="label" for="data_count">数据量</label>
                        <input type="number" class="input" id="data_count" name="data_count" 
                               placeholder="0" min="1" readonly>
                        <div class="help-text">选中的数据条数</div>
                    </div>
                </div>

                <div class="form-row full-width">
                    <div class="form-group">
                        <label class="label">选择数据</label>
                        <div class="data-selector" id="data-selector">
                            <!-- 动态加载数据项 -->
                            <div class="data-item" data-id="1001">
                                <strong>Q1001:</strong> 解方程：2x + 3 = 7，x的值是多少？
                                <div class="text-xs text-gray-500 mt-1">单选题 | 难度: 2级 | 未标注</div>
                            </div>
                            <div class="data-item" data-id="1002">
                                <strong>Q1002:</strong> 下列哪些是一元二次方程的解法？
                                <div class="text-xs text-gray-500 mt-1">多选题 | 难度: 3级 | 未标注</div>
                            </div>
                            <div class="data-item" data-id="1003">
                                <strong>Q1003:</strong> 函数 f(x) = 2x + 1 在 x = 3 时的函数值是 ______。
                                <div class="text-xs text-gray-500 mt-1">填空题 | 难度: 2级 | 未标注</div>
                            </div>
                        </div>
                        <div class="selected-count" id="selected-count">
                            已选择 0 个数据项
                        </div>
                        <div class="help-text">点击选择需要标注的数据项</div>
                    </div>
                </div>
            </div>

            <!-- 标注配置 -->
            <div class="form-section">
                <h2 class="section-title">标注配置</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="annotation_schema">标注模式</label>
                        <select class="select" id="annotation_schema" name="annotation_schema">
                            <option value="single">单人标注</option>
                            <option value="double">双人标注</option>
                            <option value="consensus">一致性标注</option>
                        </select>
                        <div class="help-text">选择标注的工作模式</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="quality_threshold">质量阈值</label>
                        <input type="number" class="input" id="quality_threshold" name="quality_threshold" 
                               step="0.01" min="0" max="1" value="0.8" placeholder="0.8">
                        <div class="help-text">标注质量的最低要求(0-1)</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="auto_assign">自动分派</label>
                        <select class="select" id="auto_assign" name="auto_assign">
                            <option value="false">否</option>
                            <option value="true">是</option>
                        </select>
                        <div class="help-text">是否自动分派给合适的标注员</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="created_by">创建人</label>
                        <input type="text" class="input" id="created_by" name="created_by" value="当前用户" readonly>
                        <div class="help-text">任务创建人</div>
                    </div>
                </div>
            </div>

            <!-- 表单操作 -->
            <div class="form-actions">
                <div>
                    <button type="button" class="btn btn-outline" onclick="saveDraft()">
                        💾 保存草稿
                    </button>
                </div>
                <div class="flex gap-2">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        🔄 重置
                    </button>
                    <button type="button" class="btn btn-outline" onclick="previewTask()">
                        👁️ 预览
                    </button>
                    <button type="submit" class="btn btn-primary">
                        ✅ 创建任务
                    </button>
                </div>
            </div>
        </form>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 标注任务表单相关功能
        let selectedDataItems = new Set();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
            initializeDataSelector();
            
            // 检查是否为编辑模式
            const urlParams = Utils.getUrlParams();
            if (urlParams.id) {
                loadTask(urlParams.id);
                document.querySelector('h1').textContent = '编辑标注任务';
            }
        });

        // 初始化表单
        function initializeForm() {
            const form = document.getElementById('task-form');
            
            // 表单提交事件
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                submitForm();
            });

            // 任务类型变化事件
            document.getElementById('task_type').addEventListener('change', function() {
                updateDataSource(this.value);
            });

            // 数据来源变化事件
            document.getElementById('data_source').addEventListener('change', function() {
                loadDataItems(this.value);
            });
        }

        // 初始化数据选择器
        function initializeDataSelector() {
            const dataSelector = document.getElementById('data-selector');
            
            dataSelector.addEventListener('click', function(e) {
                const dataItem = e.target.closest('.data-item');
                if (dataItem) {
                    toggleDataItem(dataItem);
                }
            });
        }

        // 切换数据项选择状态
        function toggleDataItem(item) {
            const dataId = item.dataset.id;
            
            if (item.classList.contains('selected')) {
                item.classList.remove('selected');
                selectedDataItems.delete(dataId);
            } else {
                item.classList.add('selected');
                selectedDataItems.add(dataId);
            }
            
            updateSelectedCount();
        }

        // 更新选中数量显示
        function updateSelectedCount() {
            const count = selectedDataItems.size;
            document.getElementById('selected-count').textContent = `已选择 ${count} 个数据项`;
            document.getElementById('data_count').value = count;
        }

        // 根据任务类型更新数据来源
        function updateDataSource(taskType) {
            const dataSource = document.getElementById('data_source');
            
            if (taskType === '0') { // 题-知识点标注
                dataSource.innerHTML = `
                    <option value="questions">题目库</option>
                    <option value="unannotated_questions">未标注题目</option>
                `;
            } else if (taskType === '1') { // 题-题关系标注
                dataSource.innerHTML = `
                    <option value="question_pairs">题目对</option>
                    <option value="similar_questions">相似题目</option>
                `;
            } else if (taskType === '2') { // 先修关系标注
                dataSource.innerHTML = `
                    <option value="knowledge_points">知识点库</option>
                    <option value="knowledge_pairs">知识点对</option>
                `;
            }
            
            loadDataItems(dataSource.value);
        }

        // 加载数据项
        function loadDataItems(source) {
            const dataSelector = document.getElementById('data-selector');
            
            // 模拟加载不同来源的数据
            let mockData = [];
            
            if (source === 'questions' || source === 'unannotated_questions') {
                mockData = [
                    { id: '1001', title: 'Q1001: 解方程：2x + 3 = 7，x的值是多少？', meta: '单选题 | 难度: 2级 | 未标注' },
                    { id: '1002', title: 'Q1002: 下列哪些是一元二次方程的解法？', meta: '多选题 | 难度: 3级 | 未标注' },
                    { id: '1003', title: 'Q1003: 函数 f(x) = 2x + 1 在 x = 3 时的函数值是 ______。', meta: '填空题 | 难度: 2级 | 未标注' }
                ];
            } else if (source === 'knowledge_points') {
                mockData = [
                    { id: 'kp_001', title: 'KP001: 一元一次方程', meta: '数学 > 代数 | 难度: 3级' },
                    { id: 'kp_002', title: 'KP002: 函数概念', meta: '数学 > 函数 | 难度: 4级' },
                    { id: 'kp_003', title: 'KP003: 英语语法', meta: '英语 > 语法 | 难度: 2级' }
                ];
            }
            
            dataSelector.innerHTML = mockData.map(item => `
                <div class="data-item" data-id="${item.id}">
                    <strong>${item.title}</strong>
                    <div class="text-xs text-gray-500 mt-1">${item.meta}</div>
                </div>
            `).join('');
            
            // 清除之前的选择
            selectedDataItems.clear();
            updateSelectedCount();
        }

        // 提交表单
        function submitForm() {
            if (selectedDataItems.size === 0) {
                Message.warning('请至少选择一个数据项');
                return;
            }

            const formData = new FormData(document.getElementById('task-form'));
            const data = Object.fromEntries(formData.entries());
            
            // 添加选中的数据项
            data.data_items = Array.from(selectedDataItems);
            data.status = 0; // 待开始
            
            console.log('提交数据:', data);

            // 模拟API调用
            Message.info('正在创建任务...');
            
            setTimeout(() => {
                Message.success('任务创建成功！');
                setTimeout(() => {
                    location.href = 'annotation-tasks.html';
                }, 1000);
            }, 2000);
        }

        // 保存草稿
        function saveDraft() {
            Message.success('草稿保存成功！');
        }

        // 重置表单
        function resetForm() {
            Modal.confirm(
                '确认重置',
                '确定要重置表单吗？所有未保存的更改将丢失。',
                () => {
                    document.getElementById('task-form').reset();
                    selectedDataItems.clear();
                    updateSelectedCount();
                    
                    // 清除选中状态
                    document.querySelectorAll('.data-item').forEach(item => {
                        item.classList.remove('selected');
                    });
                    
                    Message.info('表单已重置');
                }
            );
        }

        // 预览任务
        function previewTask() {
            const formData = new FormData(document.getElementById('task-form'));
            const data = Object.fromEntries(formData.entries());
            
            const taskTypeNames = {
                '0': '题-知识点标注',
                '1': '题-题关系标注', 
                '2': '先修关系标注'
            };
            
            const priorityNames = {
                '1': '低', '2': '中', '3': '高', '4': '紧急'
            };

            const previewContent = `
                <div class="space-y-4">
                    <div><strong>任务标题：</strong>${data.title || '未填写'}</div>
                    <div><strong>任务类型：</strong>${taskTypeNames[data.task_type] || '未选择'}</div>
                    <div><strong>优先级：</strong>${priorityNames[data.priority] || '中'}</div>
                    <div><strong>分派给：</strong>${data.assigned_to || '未指定'}</div>
                    <div><strong>截止时间：</strong>${data.deadline || '未设置'}</div>
                    <div><strong>数据量：</strong>${selectedDataItems.size} 个数据项</div>
                    <div><strong>描述：</strong>${data.description || '无'}</div>
                </div>
            `;

            Modal.show('任务预览', previewContent);
        }

        // 加载任务数据（编辑模式）
        function loadTask(id) {
            // 模拟加载数据
            const mockData = {
                task_id: id,
                task_type: '0',
                title: '英语语法知识点标注',
                priority: '3',
                assigned_to: 'user_1001',
                deadline: '2024-01-25T23:59',
                description: '对英语语法相关的200道题目进行知识点标注，包括时态、语态、从句等内容。',
                data_source: 'questions',
                annotation_schema: 'single',
                quality_threshold: '0.8',
                auto_assign: 'false'
            };

            // 填充表单
            Object.keys(mockData).forEach(key => {
                const field = document.getElementById(key);
                if (field) {
                    field.value = mockData[key];
                }
            });

            // 模拟选中的数据项
            selectedDataItems = new Set(['1001', '1002', '1003']);
            updateSelectedCount();
            
            // 更新UI选中状态
            setTimeout(() => {
                selectedDataItems.forEach(dataId => {
                    const item = document.querySelector(`[data-id="${dataId}"]`);
                    if (item) {
                        item.classList.add('selected');
                    }
                });
            }, 100);
        }
    </script>
</body>
</html>
