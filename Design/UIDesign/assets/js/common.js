// 自适应学习服务数据标注模块 - 公共JavaScript

// 全局配置
const CONFIG = {
  API_BASE_URL: '/api/v1',
  PAGE_SIZE: 20,
  DEBOUNCE_DELAY: 300
};

// 工具函数
const Utils = {
  // 防抖函数
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // 格式化日期
  formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  },

  // 生成UUID
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  // 深拷贝
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  },

  // 获取URL参数
  getUrlParams() {
    const params = {};
    const urlSearchParams = new URLSearchParams(window.location.search);
    for (const [key, value] of urlSearchParams) {
      params[key] = value;
    }
    return params;
  }
};

// HTTP请求封装
const Http = {
  async request(url, options = {}) {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('token') || ''
      }
    };

    const config = { ...defaultOptions, ...options };
    
    try {
      const response = await fetch(CONFIG.API_BASE_URL + url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Request failed:', error);
      throw error;
    }
  },

  get(url, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    return this.request(fullUrl);
  },

  post(url, data) {
    return this.request(url, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  put(url, data) {
    return this.request(url, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  },

  delete(url) {
    return this.request(url, {
      method: 'DELETE'
    });
  }
};

// 消息提示组件
const Message = {
  show(content, type = 'info', duration = 3000) {
    const messageEl = document.createElement('div');
    messageEl.className = `alert alert-${type}`;
    messageEl.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      min-width: 300px;
      animation: slideInRight 0.3s ease-out;
    `;
    messageEl.textContent = content;

    document.body.appendChild(messageEl);

    setTimeout(() => {
      messageEl.style.animation = 'slideOutRight 0.3s ease-out';
      setTimeout(() => {
        document.body.removeChild(messageEl);
      }, 300);
    }, duration);
  },

  success(content, duration) {
    this.show(content, 'success', duration);
  },

  error(content, duration) {
    this.show(content, 'error', duration);
  },

  warning(content, duration) {
    this.show(content, 'warning', duration);
  },

  info(content, duration) {
    this.show(content, 'info', duration);
  }
};

// 模态框组件
const Modal = {
  show(title, content, options = {}) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    `;

    const modalContent = document.createElement('div');
    modalContent.className = 'card';
    modalContent.style.cssText = `
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
    `;

    modalContent.innerHTML = `
      <div class="card-header">
        <div class="flex items-center justify-between">
          <h3 class="card-title">${title}</h3>
          <button class="btn btn-sm" onclick="this.closest('.modal-overlay').remove()">×</button>
        </div>
      </div>
      <div class="card-body">
        ${content}
      </div>
      ${options.footer ? `<div class="card-footer">${options.footer}</div>` : ''}
    `;

    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // 点击遮罩关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });

    return modal;
  },

  confirm(title, content, onConfirm) {
    const footer = `
      <div class="flex gap-2 justify-end">
        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
        <button class="btn btn-primary" onclick="(${onConfirm})(); this.closest('.modal-overlay').remove()">确认</button>
      </div>
    `;
    return this.show(title, content, { footer });
  }
};

// 表格组件
class DataTable {
  constructor(container, options = {}) {
    this.container = typeof container === 'string' ? document.querySelector(container) : container;
    this.options = {
      columns: [],
      data: [],
      pagination: true,
      pageSize: CONFIG.PAGE_SIZE,
      searchable: true,
      sortable: true,
      ...options
    };
    this.currentPage = 1;
    this.sortColumn = null;
    this.sortDirection = 'asc';
    this.searchTerm = '';
    
    this.init();
  }

  init() {
    this.render();
    this.bindEvents();
  }

  render() {
    const filteredData = this.getFilteredData();
    const paginatedData = this.getPaginatedData(filteredData);

    this.container.innerHTML = `
      ${this.options.searchable ? this.renderSearch() : ''}
      ${this.renderTable(paginatedData)}
      ${this.options.pagination ? this.renderPagination(filteredData.length) : ''}
    `;
  }

  renderSearch() {
    return `
      <div class="mb-4">
        <input type="text" class="input" placeholder="搜索..." id="table-search">
      </div>
    `;
  }

  renderTable(data) {
    return `
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              ${this.options.columns.map(col => `
                <th ${this.options.sortable ? `style="cursor: pointer;" data-sort="${col.key}"` : ''}>
                  ${col.title}
                  ${this.sortColumn === col.key ? (this.sortDirection === 'asc' ? ' ↑' : ' ↓') : ''}
                </th>
              `).join('')}
            </tr>
          </thead>
          <tbody>
            ${data.map(row => `
              <tr>
                ${this.options.columns.map(col => `
                  <td>${this.formatCellValue(row[col.key], col)}</td>
                `).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  renderPagination(totalItems) {
    const totalPages = Math.ceil(totalItems / this.options.pageSize);
    if (totalPages <= 1) return '';

    let pages = [];
    for (let i = 1; i <= totalPages; i++) {
      pages.push(`
        <a href="#" class="pagination-item ${i === this.currentPage ? 'active' : ''}" data-page="${i}">
          ${i}
        </a>
      `);
    }

    return `
      <div class="pagination">
        <a href="#" class="pagination-item" data-page="${this.currentPage - 1}" ${this.currentPage === 1 ? 'style="display:none;"' : ''}>
          上一页
        </a>
        ${pages.join('')}
        <a href="#" class="pagination-item" data-page="${this.currentPage + 1}" ${this.currentPage === totalPages ? 'style="display:none;"' : ''}>
          下一页
        </a>
      </div>
    `;
  }

  formatCellValue(value, column) {
    if (column.render) {
      return column.render(value);
    }
    if (column.type === 'date') {
      return Utils.formatDate(value, 'YYYY-MM-DD');
    }
    if (column.type === 'datetime') {
      return Utils.formatDate(value);
    }
    return value || '';
  }

  getFilteredData() {
    let data = [...this.options.data];
    
    if (this.searchTerm) {
      data = data.filter(row => 
        this.options.columns.some(col => 
          String(row[col.key] || '').toLowerCase().includes(this.searchTerm.toLowerCase())
        )
      );
    }

    if (this.sortColumn) {
      data.sort((a, b) => {
        const aVal = a[this.sortColumn];
        const bVal = b[this.sortColumn];
        
        if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
        if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return data;
  }

  getPaginatedData(data) {
    if (!this.options.pagination) return data;
    
    const start = (this.currentPage - 1) * this.options.pageSize;
    const end = start + this.options.pageSize;
    return data.slice(start, end);
  }

  bindEvents() {
    // 搜索事件
    const searchInput = this.container.querySelector('#table-search');
    if (searchInput) {
      searchInput.addEventListener('input', Utils.debounce((e) => {
        this.searchTerm = e.target.value;
        this.currentPage = 1;
        this.render();
      }, CONFIG.DEBOUNCE_DELAY));
    }

    // 排序事件
    this.container.addEventListener('click', (e) => {
      if (e.target.hasAttribute('data-sort')) {
        const column = e.target.getAttribute('data-sort');
        if (this.sortColumn === column) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortColumn = column;
          this.sortDirection = 'asc';
        }
        this.render();
      }
    });

    // 分页事件
    this.container.addEventListener('click', (e) => {
      if (e.target.hasAttribute('data-page')) {
        e.preventDefault();
        const page = parseInt(e.target.getAttribute('data-page'));
        if (page > 0 && page <= Math.ceil(this.getFilteredData().length / this.options.pageSize)) {
          this.currentPage = page;
          this.render();
        }
      }
    });
  }

  updateData(newData) {
    this.options.data = newData;
    this.currentPage = 1;
    this.render();
  }
}

// 表单验证
const FormValidator = {
  rules: {
    required: (value) => value !== null && value !== undefined && value !== '',
    email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    phone: (value) => /^1[3-9]\d{9}$/.test(value),
    minLength: (min) => (value) => value && value.length >= min,
    maxLength: (max) => (value) => value && value.length <= max,
    range: (min, max) => (value) => {
      const num = parseFloat(value);
      return !isNaN(num) && num >= min && num <= max;
    }
  },

  validate(form, rules) {
    const errors = {};
    
    Object.keys(rules).forEach(field => {
      const fieldRules = rules[field];
      const input = form.querySelector(`[name="${field}"]`);
      const value = input ? input.value : '';
      
      fieldRules.forEach(rule => {
        if (typeof rule === 'string') {
          if (!this.rules[rule](value)) {
            errors[field] = errors[field] || [];
            errors[field].push(this.getErrorMessage(rule, field));
          }
        } else if (typeof rule === 'object') {
          const { type, params, message } = rule;
          if (!this.rules[type](...params)(value)) {
            errors[field] = errors[field] || [];
            errors[field].push(message || this.getErrorMessage(type, field));
          }
        }
      });
    });

    this.displayErrors(form, errors);
    return Object.keys(errors).length === 0;
  },

  getErrorMessage(rule, field) {
    const messages = {
      required: `${field}是必填项`,
      email: `${field}格式不正确`,
      phone: `${field}格式不正确`,
      minLength: `${field}长度不足`,
      maxLength: `${field}长度超限`,
      range: `${field}数值超出范围`
    };
    return messages[rule] || `${field}验证失败`;
  },

  displayErrors(form, errors) {
    // 清除之前的错误信息
    form.querySelectorAll('.error-text').forEach(el => el.remove());
    form.querySelectorAll('.input').forEach(el => el.classList.remove('error'));

    // 显示新的错误信息
    Object.keys(errors).forEach(field => {
      const input = form.querySelector(`[name="${field}"]`);
      if (input) {
        input.classList.add('error');
        const errorEl = document.createElement('div');
        errorEl.className = 'error-text';
        errorEl.textContent = errors[field][0];
        input.parentNode.appendChild(errorEl);
      }
    });
  }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
  // 移动端侧边栏切换
  const menuToggle = document.querySelector('.menu-toggle');
  const sidebar = document.querySelector('.sidebar');
  
  if (menuToggle && sidebar) {
    menuToggle.addEventListener('click', () => {
      sidebar.classList.toggle('open');
    });
  }

  // 添加动画样式
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideInRight {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
      from { transform: translateX(0); opacity: 1; }
      to { transform: translateX(100%); opacity: 0; }
    }
  `;
  document.head.appendChild(style);
});

// 导出全局对象
window.Utils = Utils;
window.Http = Http;
window.Message = Message;
window.Modal = Modal;
window.DataTable = DataTable;
window.FormValidator = FormValidator;
