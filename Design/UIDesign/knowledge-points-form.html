<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识点表单 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .form-section {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .section-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-4);
            color: var(--gray-900);
            border-bottom: 2px solid var(--primary-500);
            padding-bottom: var(--space-2);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-4);
            margin-bottom: var(--space-4);
        }
        
        .form-row.full-width {
            grid-template-columns: 1fr;
        }
        
        .prerequisite-selector {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            background: var(--gray-50);
        }
        
        .prerequisite-search {
            margin-bottom: var(--space-3);
        }
        
        .prerequisite-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            background: white;
        }
        
        .prerequisite-item {
            padding: var(--space-2) var(--space-3);
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: background-color 0.15s ease;
        }
        
        .prerequisite-item:hover {
            background-color: var(--gray-50);
        }
        
        .prerequisite-item.selected {
            background-color: var(--primary-50);
            color: var(--primary-700);
        }
        
        .selected-prerequisites {
            margin-top: var(--space-3);
        }
        
        .selected-prerequisite {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            background: var(--primary-100);
            color: var(--primary-800);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-md);
            margin: var(--space-1);
            font-size: var(--text-sm);
        }
        
        .remove-prerequisite {
            cursor: pointer;
            font-weight: bold;
        }
        
        .form-actions {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .validation-summary {
            background: var(--error-50);
            border: 1px solid var(--error-200);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
            display: none;
        }
        
        .validation-title {
            font-weight: var(--font-semibold);
            color: var(--error-700);
            margin-bottom: var(--space-2);
        }
        
        .validation-list {
            list-style: none;
            padding: 0;
        }
        
        .validation-list li {
            color: var(--error-600);
            margin-bottom: var(--space-1);
        }
        
        .validation-list li:before {
            content: "• ";
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">首页</a>
                <a href="knowledge-space.html" class="nav-link active">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link">标注工作流</a>
                <a href="quality-control.html" class="nav-link">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item active">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">新建知识点</h1>
                    <p class="text-gray-600">创建新的知识点并设置相关属性</p>
                </div>
                <div>
                    <button class="btn btn-outline" onclick="history.back()">
                        ← 返回
                    </button>
                </div>
            </div>
        </div>

        <!-- 验证错误摘要 -->
        <div class="validation-summary" id="validation-summary">
            <div class="validation-title">请修正以下错误：</div>
            <ul class="validation-list" id="validation-list">
            </ul>
        </div>

        <form id="knowledge-point-form" class="form-container">
            <!-- 基本信息 -->
            <div class="form-section">
                <h2 class="section-title">基本信息</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="name">知识点名称 *</label>
                        <input type="text" class="input" id="name" name="name" placeholder="请输入知识点名称" required>
                        <div class="help-text">知识点的标准名称，用于显示和检索</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="code">知识点编码</label>
                        <input type="text" class="input" id="code" name="code" placeholder="如：MATH_ALG_001">
                        <div class="help-text">唯一标识符，可留空自动生成</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="parent_id">父级知识点</label>
                        <select class="select" id="parent_id" name="parent_id">
                            <option value="">选择父级知识点</option>
                            <option value="1">数学</option>
                            <option value="2">数学 > 代数</option>
                            <option value="3">数学 > 几何</option>
                            <option value="6">英语</option>
                            <option value="7">英语 > 语法</option>
                        </select>
                        <div class="help-text">选择此知识点的上级分类</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="difficulty_level">难度等级</label>
                        <select class="select" id="difficulty_level" name="difficulty_level">
                            <option value="">选择难度等级</option>
                            <option value="1">1级 - 基础</option>
                            <option value="2">2级 - 简单</option>
                            <option value="3">3级 - 中等</option>
                            <option value="4">4级 - 困难</option>
                            <option value="5">5级 - 极难</option>
                        </select>
                        <div class="help-text">知识点的学习难度等级(1-5)</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="is_leaf">是否叶子节点</label>
                        <select class="select" id="is_leaf" name="is_leaf">
                            <option value="false">否 - 分类节点</option>
                            <option value="true">是 - 叶子节点</option>
                        </select>
                        <div class="help-text">叶子节点可以关联题目，分类节点用于组织结构</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="created_by">创建人</label>
                        <input type="text" class="input" id="created_by" name="created_by" value="当前用户" readonly>
                        <div class="help-text">系统自动填充当前用户</div>
                    </div>
                </div>

                <div class="form-row full-width">
                    <div class="form-group">
                        <label class="label" for="description">知识点描述</label>
                        <textarea class="textarea" id="description" name="description" rows="4"
                                placeholder="请详细描述这个知识点的内容、学习目标和应用场景"></textarea>
                        <div class="help-text">详细描述有助于标注员理解知识点的内涵和外延</div>
                    </div>
                </div>
            </div>

            <!-- 先修关系设置 -->
            <div class="form-section">
                <h2 class="section-title">先修关系设置</h2>
                
                <div class="form-group">
                    <label class="label">选择先修知识点</label>
                    <div class="prerequisite-selector">
                        <div class="prerequisite-search">
                            <input type="text" class="input" placeholder="搜索知识点..." id="prerequisite-search">
                        </div>
                        
                        <div class="prerequisite-list" id="prerequisite-list">
                            <div class="prerequisite-item" data-id="101" data-name="有理数运算">
                                <strong>有理数运算</strong>
                                <div class="text-xs text-gray-500">数学 > 代数基础</div>
                            </div>
                            <div class="prerequisite-item" data-id="102" data-name="代数式">
                                <strong>代数式</strong>
                                <div class="text-xs text-gray-500">数学 > 代数基础</div>
                            </div>
                            <div class="prerequisite-item" data-id="103" data-name="等式性质">
                                <strong>等式性质</strong>
                                <div class="text-xs text-gray-500">数学 > 代数基础</div>
                            </div>
                            <div class="prerequisite-item" data-id="104" data-name="不等式">
                                <strong>不等式</strong>
                                <div class="text-xs text-gray-500">数学 > 代数基础</div>
                            </div>
                        </div>
                        
                        <div class="selected-prerequisites" id="selected-prerequisites">
                            <div class="text-sm font-medium mb-2">已选择的先修知识点：</div>
                            <!-- 动态添加选中的先修知识点 -->
                        </div>
                    </div>
                    <div class="help-text">选择学习此知识点前必须掌握的其他知识点</div>
                </div>
            </div>

            <!-- 路径信息 -->
            <div class="form-section">
                <h2 class="section-title">路径信息</h2>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="path">知识点路径</label>
                        <input type="text" class="input" id="path" name="path"
                               placeholder="系统自动生成" readonly>
                        <div class="help-text">LTREE格式的层级路径，系统自动维护</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="kp_id">知识点ID</label>
                        <input type="text" class="input" id="kp_id" name="kp_id"
                               placeholder="系统自动分配" readonly>
                        <div class="help-text">系统自动分配的唯一标识符</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="label" for="created_at">创建时间</label>
                        <input type="text" class="input" id="created_at" name="created_at"
                               placeholder="系统自动记录" readonly>
                        <div class="help-text">知识点创建的时间戳</div>
                    </div>
                    <div class="form-group">
                        <label class="label" for="updated_at">更新时间</label>
                        <input type="text" class="input" id="updated_at" name="updated_at"
                               placeholder="系统自动更新" readonly>
                        <div class="help-text">最后修改的时间戳</div>
                    </div>
                </div>
            </div>

            <!-- 表单操作 -->
            <div class="form-actions">
                <div>
                    <button type="button" class="btn btn-outline" onclick="saveDraft()">
                        💾 保存草稿
                    </button>
                </div>
                <div class="flex gap-2">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        🔄 重置
                    </button>
                    <button type="button" class="btn btn-outline" onclick="previewKnowledgePoint()">
                        👁️ 预览
                    </button>
                    <button type="submit" class="btn btn-primary">
                        ✅ 保存并发布
                    </button>
                </div>
            </div>
        </form>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 表单相关变量
        let selectedPrerequisites = [];
        let isEditMode = false;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
            initializePrerequisiteSelector();
            
            // 检查是否为编辑模式
            const urlParams = Utils.getUrlParams();
            if (urlParams.id) {
                isEditMode = true;
                loadKnowledgePoint(urlParams.id);
                document.querySelector('h1').textContent = '编辑知识点';
            }
        });

        // 初始化表单
        function initializeForm() {
            const form = document.getElementById('knowledge-point-form');
            
            // 表单提交事件
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                submitForm();
            });

            // 实时验证
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });
            });

            // 自动生成编码
            document.getElementById('name').addEventListener('input', function() {
                generateCode();
            });
        }

        // 初始化先修知识点选择器
        function initializePrerequisiteSelector() {
            const searchInput = document.getElementById('prerequisite-search');
            const prerequisiteList = document.getElementById('prerequisite-list');
            
            // 搜索功能
            searchInput.addEventListener('input', Utils.debounce(function() {
                filterPrerequisites(this.value);
            }, 300));

            // 点击选择先修知识点
            prerequisiteList.addEventListener('click', function(e) {
                const item = e.target.closest('.prerequisite-item');
                if (item) {
                    togglePrerequisite(item);
                }
            });
        }

        // 筛选先修知识点
        function filterPrerequisites(searchTerm) {
            const items = document.querySelectorAll('.prerequisite-item');
            
            items.forEach(item => {
                const name = item.dataset.name.toLowerCase();
                const visible = name.includes(searchTerm.toLowerCase());
                item.style.display = visible ? 'block' : 'none';
            });
        }

        // 切换先修知识点选择状态
        function togglePrerequisite(item) {
            const id = parseInt(item.dataset.id);
            const name = item.dataset.name;
            
            if (item.classList.contains('selected')) {
                // 取消选择
                item.classList.remove('selected');
                selectedPrerequisites = selectedPrerequisites.filter(p => p.id !== id);
            } else {
                // 选择
                item.classList.add('selected');
                selectedPrerequisites.push({ id, name });
            }
            
            updateSelectedPrerequisites();
        }

        // 更新已选择的先修知识点显示
        function updateSelectedPrerequisites() {
            const container = document.getElementById('selected-prerequisites');
            const titleDiv = container.querySelector('.text-sm');
            
            // 清除现有的选中项（保留标题）
            const existingItems = container.querySelectorAll('.selected-prerequisite');
            existingItems.forEach(item => item.remove());
            
            // 添加新的选中项
            selectedPrerequisites.forEach(prerequisite => {
                const item = document.createElement('span');
                item.className = 'selected-prerequisite';
                item.innerHTML = `
                    ${prerequisite.name}
                    <span class="remove-prerequisite" onclick="removePrerequisite(${prerequisite.id})">×</span>
                `;
                container.appendChild(item);
            });
        }

        // 移除先修知识点
        function removePrerequisite(id) {
            selectedPrerequisites = selectedPrerequisites.filter(p => p.id !== id);
            
            // 更新UI
            const item = document.querySelector(`[data-id="${id}"]`);
            if (item) {
                item.classList.remove('selected');
            }
            
            updateSelectedPrerequisites();
        }

        // 自动生成编码
        function generateCode() {
            const name = document.getElementById('name').value;
            const subject = document.getElementById('subject').value;
            
            if (name && subject) {
                // 简单的编码生成逻辑
                const subjectCode = {
                    'math': 'MATH',
                    'english': 'ENG',
                    'physics': 'PHY',
                    'chemistry': 'CHEM',
                    'biology': 'BIO'
                };
                
                const code = `${subjectCode[subject] || 'UNKNOWN'}_${name.substring(0, 3).toUpperCase()}_${Date.now().toString().slice(-3)}`;
                document.getElementById('code').value = code;
            }
        }

        // 验证单个字段
        function validateField(field) {
            const rules = {
                name: ['required', { type: 'minLength', params: [2], message: '知识点名称至少2个字符' }],
                code: ['required', { type: 'minLength', params: [3], message: '知识点编码至少3个字符' }],
                difficulty: ['required'],
                subject: ['required'],
                status: ['required']
            };

            if (rules[field.name]) {
                const isValid = FormValidator.validate(field.form, { [field.name]: rules[field.name] });
                return isValid;
            }
            
            return true;
        }

        // 验证整个表单
        function validateForm() {
            const rules = {
                name: ['required', { type: 'minLength', params: [2], message: '知识点名称至少2个字符' }],
                code: ['required', { type: 'minLength', params: [3], message: '知识点编码至少3个字符' }],
                difficulty: ['required'],
                subject: ['required'],
                status: ['required']
            };

            const form = document.getElementById('knowledge-point-form');
            const isValid = FormValidator.validate(form, rules);
            
            if (!isValid) {
                showValidationSummary();
            } else {
                hideValidationSummary();
            }
            
            return isValid;
        }

        // 显示验证错误摘要
        function showValidationSummary() {
            const summary = document.getElementById('validation-summary');
            const list = document.getElementById('validation-list');
            
            // 收集所有错误信息
            const errors = document.querySelectorAll('.error-text');
            list.innerHTML = '';
            
            errors.forEach(error => {
                const li = document.createElement('li');
                li.textContent = error.textContent;
                list.appendChild(li);
            });
            
            summary.style.display = 'block';
            summary.scrollIntoView({ behavior: 'smooth' });
        }

        // 隐藏验证错误摘要
        function hideValidationSummary() {
            document.getElementById('validation-summary').style.display = 'none';
        }

        // 提交表单
        function submitForm() {
            if (!validateForm()) {
                return;
            }

            const formData = new FormData(document.getElementById('knowledge-point-form'));
            const data = Object.fromEntries(formData.entries());
            
            // 添加先修知识点数据
            data.prerequisites = selectedPrerequisites.map(p => p.id);
            
            // 处理标签
            if (data.tags) {
                data.tags = data.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
            }

            console.log('提交数据:', data);

            // 模拟API调用
            Message.info('正在保存知识点...');
            
            setTimeout(() => {
                Message.success('知识点保存成功！');
                setTimeout(() => {
                    location.href = 'knowledge-points.html';
                }, 1000);
            }, 2000);
        }

        // 保存草稿
        function saveDraft() {
            const formData = new FormData(document.getElementById('knowledge-point-form'));
            const data = Object.fromEntries(formData.entries());
            data.status = 'draft';
            data.prerequisites = selectedPrerequisites.map(p => p.id);

            console.log('保存草稿:', data);
            Message.success('草稿保存成功！');
        }

        // 重置表单
        function resetForm() {
            Modal.confirm(
                '确认重置',
                '确定要重置表单吗？所有未保存的更改将丢失。',
                () => {
                    document.getElementById('knowledge-point-form').reset();
                    selectedPrerequisites = [];
                    updateSelectedPrerequisites();
                    
                    // 清除选中状态
                    document.querySelectorAll('.prerequisite-item').forEach(item => {
                        item.classList.remove('selected');
                    });
                    
                    hideValidationSummary();
                    Message.info('表单已重置');
                }
            );
        }

        // 预览知识点
        function previewKnowledgePoint() {
            const formData = new FormData(document.getElementById('knowledge-point-form'));
            const data = Object.fromEntries(formData.entries());
            data.prerequisites = selectedPrerequisites;

            const previewContent = `
                <div class="space-y-4">
                    <div><strong>名称：</strong>${data.name || '未填写'}</div>
                    <div><strong>编码：</strong>${data.code || '未填写'}</div>
                    <div><strong>难度等级：</strong>${data.difficulty || '未选择'}</div>
                    <div><strong>学科分类：</strong>${data.subject || '未选择'}</div>
                    <div><strong>状态：</strong>${data.status || '未选择'}</div>
                    <div><strong>描述：</strong>${data.description || '无'}</div>
                    <div><strong>先修知识点：</strong>${selectedPrerequisites.map(p => p.name).join(', ') || '无'}</div>
                </div>
            `;

            Modal.show('知识点预览', previewContent);
        }

        // 加载知识点数据（编辑模式）
        function loadKnowledgePoint(id) {
            // 模拟加载数据
            const mockData = {
                name: '一元一次方程',
                code: 'MATH_ALG_001',
                parent: '2',
                difficulty: '3',
                subject: 'math',
                status: 'active',
                description: '一元一次方程是只含有一个未知数，并且未知数的最高次数是1的方程。',
                learning_time: '90',
                importance: 'high',
                tags: '基础,核心,必修',
                notes: '这是代数学习的基础内容'
            };

            // 填充表单
            Object.keys(mockData).forEach(key => {
                const field = document.getElementById(key);
                if (field) {
                    field.value = mockData[key];
                }
            });

            // 模拟加载先修知识点
            selectedPrerequisites = [
                { id: 101, name: '有理数运算' },
                { id: 102, name: '代数式' },
                { id: 103, name: '等式性质' }
            ];
            
            updateSelectedPrerequisites();
            
            // 更新UI选中状态
            selectedPrerequisites.forEach(prerequisite => {
                const item = document.querySelector(`[data-id="${prerequisite.id}"]`);
                if (item) {
                    item.classList.add('selected');
                }
            });
        }
    </script>
</body>
</html>
