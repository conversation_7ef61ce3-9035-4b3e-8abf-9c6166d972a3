<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI设计导航 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
            color: white;
            padding: var(--space-16) 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: var(--font-bold);
            margin-bottom: var(--space-4);
        }
        
        .hero-subtitle {
            font-size: var(--text-xl);
            opacity: 0.9;
            margin-bottom: var(--space-8);
        }
        
        .page-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--space-6);
            padding: var(--space-8) 0;
        }
        
        .page-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .page-card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-4px);
        }
        
        .page-preview {
            height: 200px;
            background: var(--gray-100);
            position: relative;
            overflow: hidden;
        }
        
        .page-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .page-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, var(--primary-500), var(--primary-600));
            opacity: 0.1;
        }
        
        .page-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 4rem;
            opacity: 0.3;
        }
        
        .page-content {
            padding: var(--space-6);
        }
        
        .page-title {
            font-size: var(--text-xl);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-2);
            color: var(--gray-900);
        }
        
        .page-description {
            color: var(--gray-600);
            margin-bottom: var(--space-4);
            line-height: 1.6;
        }
        
        .page-features {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-2);
            margin-bottom: var(--space-4);
        }
        
        .feature-tag {
            background: var(--primary-100);
            color: var(--primary-700);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
        }
        
        .page-actions {
            display: flex;
            gap: var(--space-2);
        }
        
        .stats-section {
            background: var(--gray-50);
            padding: var(--space-8) 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-6);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            color: var(--primary-600);
            margin-bottom: var(--space-2);
        }
        
        .stat-label {
            color: var(--gray-600);
            font-size: var(--text-sm);
        }
        
        .tech-stack {
            background: white;
            padding: var(--space-8) 0;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
        }
        
        .tech-item {
            text-align: center;
            padding: var(--space-6);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
        }
        
        .tech-icon {
            font-size: 3rem;
            margin-bottom: var(--space-3);
        }
        
        .tech-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-2);
        }
        
        .tech-description {
            color: var(--gray-600);
            font-size: var(--text-sm);
        }
    </style>
</head>
<body>
    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title">UI设计参考</h1>
            <p class="hero-subtitle">自适应学习服务数据标注模块界面设计</p>
            <div class="flex gap-4 justify-center">
                <button class="btn btn-lg" style="background: white; color: var(--primary-600);" onclick="scrollToPages()">
                    🎨 浏览页面
                </button>
                <button class="btn btn-lg btn-outline" style="border-color: white; color: white;" onclick="viewDocumentation()">
                    📚 查看文档
                </button>
            </div>
        </div>
    </section>

    <!-- 统计信息 -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">8+</div>
                    <div class="stat-label">核心页面</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">UI组件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">响应式设计</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">WCAG 2.1</div>
                    <div class="stat-label">可访问性标准</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页面展示 -->
    <section class="container" id="pages-section">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold mb-4">页面展示</h2>
            <p class="text-gray-600">完整的功能页面设计，包含CRUD操作、搜索筛选、表单处理等核心功能</p>
        </div>
        
        <div class="page-grid">
            <!-- 系统首页 -->
            <div class="page-card" onclick="openPage('index.html')">
                <div class="page-preview">
                    <div class="page-icon">🏠</div>
                </div>
                <div class="page-content">
                    <h3 class="page-title">系统首页</h3>
                    <p class="page-description">系统概览仪表板，展示关键指标、快速操作和最近活动，为用户提供系统全貌。</p>
                    <div class="page-features">
                        <span class="feature-tag">统计卡片</span>
                        <span class="feature-tag">快速操作</span>
                        <span class="feature-tag">活动时间线</span>
                        <span class="feature-tag">响应式布局</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary btn-sm">查看页面</button>
                        <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); viewCode('index.html')">查看代码</button>
                    </div>
                </div>
            </div>

            <!-- 知识点管理 -->
            <div class="page-card" onclick="openPage('knowledge-points.html')">
                <div class="page-preview">
                    <div class="page-icon">🧠</div>
                </div>
                <div class="page-content">
                    <h3 class="page-title">知识点管理</h3>
                    <p class="page-description">树形结构展示知识点层级，支持搜索筛选、详情查看和关系管理。</p>
                    <div class="page-features">
                        <span class="feature-tag">树形结构</span>
                        <span class="feature-tag">搜索筛选</span>
                        <span class="feature-tag">详情面板</span>
                        <span class="feature-tag">批量操作</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary btn-sm">查看页面</button>
                        <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); viewCode('knowledge-points.html')">查看代码</button>
                    </div>
                </div>
            </div>

            <!-- 知识点表单 -->
            <div class="page-card" onclick="openPage('knowledge-points-form.html')">
                <div class="page-preview">
                    <div class="page-icon">📝</div>
                </div>
                <div class="page-content">
                    <h3 class="page-title">知识点表单</h3>
                    <p class="page-description">新建和编辑知识点的表单页面，包含验证、预览和先修关系设置。</p>
                    <div class="page-features">
                        <span class="feature-tag">分步表单</span>
                        <span class="feature-tag">实时验证</span>
                        <span class="feature-tag">关系选择器</span>
                        <span class="feature-tag">预览功能</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary btn-sm">查看页面</button>
                        <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); viewCode('knowledge-points-form.html')">查看代码</button>
                    </div>
                </div>
            </div>

            <!-- 题目管理 -->
            <div class="page-card" onclick="openPage('questions.html')">
                <div class="page-preview">
                    <div class="page-icon">📋</div>
                </div>
                <div class="page-content">
                    <h3 class="page-title">题目管理</h3>
                    <p class="page-description">题目库管理界面，支持题目预览、批量操作和多维度筛选。</p>
                    <div class="page-features">
                        <span class="feature-tag">题目预览</span>
                        <span class="feature-tag">批量选择</span>
                        <span class="feature-tag">状态跟踪</span>
                        <span class="feature-tag">导入导出</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary btn-sm">查看页面</button>
                        <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); viewCode('questions.html')">查看代码</button>
                    </div>
                </div>
            </div>

            <!-- 标注任务 -->
            <div class="page-card" onclick="openPage('annotation-tasks.html')">
                <div class="page-preview">
                    <div class="page-icon">✏️</div>
                </div>
                <div class="page-content">
                    <h3 class="page-title">标注任务管理</h3>
                    <p class="page-description">看板式任务管理界面，支持任务状态跟踪和多视图切换。</p>
                    <div class="page-features">
                        <span class="feature-tag">看板视图</span>
                        <span class="feature-tag">进度跟踪</span>
                        <span class="feature-tag">状态管理</span>
                        <span class="feature-tag">多视图</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary btn-sm">查看页面</button>
                        <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); viewCode('annotation-tasks.html')">查看代码</button>
                    </div>
                </div>
            </div>

            <!-- 质量报告 -->
            <div class="page-card" onclick="openPage('quality-reports.html')">
                <div class="page-preview">
                    <div class="page-icon">📊</div>
                </div>
                <div class="page-content">
                    <h3 class="page-title">质量报告</h3>
                    <p class="page-description">质量监控仪表板，展示质量指标、趋势分析和问题跟踪。</p>
                    <div class="page-features">
                        <span class="feature-tag">质量指标</span>
                        <span class="feature-tag">趋势图表</span>
                        <span class="feature-tag">问题跟踪</span>
                        <span class="feature-tag">表现分析</span>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary btn-sm">查看页面</button>
                        <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); viewCode('quality-reports.html')">查看代码</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技术栈 -->
    <section class="tech-stack">
        <div class="container">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold mb-4">技术栈</h2>
                <p class="text-gray-600">基于现代化Web技术构建的设计系统</p>
            </div>
            
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="tech-icon">⚛️</div>
                    <h3 class="tech-title">React</h3>
                    <p class="tech-description">现代化的前端框架，提供组件化开发和状态管理</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">🎨</div>
                    <h3 class="tech-title">Radix UI</h3>
                    <p class="tech-description">无样式的UI组件库，提供可访问性和可定制性</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">🎯</div>
                    <h3 class="tech-title">Tailwind CSS</h3>
                    <p class="tech-description">实用优先的CSS框架，快速构建现代化界面</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">📱</div>
                    <h3 class="tech-title">响应式设计</h3>
                    <p class="tech-description">适配桌面端、平板端和移动端的响应式布局</p>
                </div>
            </div>
        </div>
    </section>

    <script src="assets/js/common.js"></script>
    <script>
        // 导航页面功能
        
        // 滚动到页面展示区域
        function scrollToPages() {
            document.getElementById('pages-section').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }
        
        // 查看文档
        function viewDocumentation() {
            window.open('README.md', '_blank');
        }
        
        // 打开页面
        function openPage(filename) {
            window.open(filename, '_blank');
        }
        
        // 查看代码
        function viewCode(filename) {
            // 这里可以实现代码查看功能
            // 比如在新窗口中显示源代码
            fetch(filename)
                .then(response => response.text())
                .then(html => {
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(`
                        <html>
                        <head>
                            <title>${filename} - 源代码</title>
                            <style>
                                body { font-family: monospace; margin: 20px; }
                                pre { background: #f5f5f5; padding: 20px; border-radius: 5px; overflow-x: auto; }
                            </style>
                        </head>
                        <body>
                            <h1>${filename} - 源代码</h1>
                            <pre><code>${html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
                        </body>
                        </html>
                    `);
                })
                .catch(error => {
                    Message.error('无法加载源代码');
                });
        }
        
        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面卡片的进入动画
            const cards = document.querySelectorAll('.page-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
