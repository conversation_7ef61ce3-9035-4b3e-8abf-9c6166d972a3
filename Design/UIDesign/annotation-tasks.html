<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标注任务管理 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .task-board {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .task-column {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            min-height: 500px;
        }
        
        .column-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-4);
            padding-bottom: var(--space-2);
            border-bottom: 2px solid var(--gray-200);
        }
        
        .column-title {
            font-weight: var(--font-semibold);
            color: var(--gray-700);
        }
        
        .column-count {
            background: var(--gray-200);
            color: var(--gray-700);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-full);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
        }
        
        .task-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            margin-bottom: var(--space-3);
            cursor: pointer;
            transition: all 0.15s ease;
        }
        
        .task-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .task-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-2);
        }
        
        .task-id {
            font-size: var(--text-sm);
            color: var(--gray-500);
            font-weight: var(--font-medium);
        }
        
        .task-priority {
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
        }
        
        .priority-high {
            background: var(--error-100);
            color: var(--error-700);
        }
        
        .priority-medium {
            background: var(--warning-100);
            color: var(--warning-700);
        }
        
        .priority-low {
            background: var(--success-100);
            color: var(--success-700);
        }
        
        .task-title {
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-2);
            color: var(--gray-900);
        }
        
        .task-description {
            font-size: var(--text-sm);
            color: var(--gray-600);
            margin-bottom: var(--space-3);
            line-height: 1.4;
        }
        
        .task-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: var(--text-xs);
            color: var(--gray-500);
        }
        
        .task-assignee {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .assignee-avatar {
            width: 24px;
            height: 24px;
            border-radius: var(--radius-full);
            background: var(--primary-100);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-600);
            font-weight: var(--font-semibold);
            font-size: var(--text-xs);
        }
        
        .task-progress {
            margin-top: var(--space-2);
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--gray-200);
            border-radius: var(--radius-full);
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--primary-500);
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: var(--text-xs);
            color: var(--gray-500);
            margin-top: var(--space-1);
        }
        
        .task-actions {
            display: flex;
            gap: var(--space-1);
            margin-top: var(--space-3);
        }
        
        .filters-bar {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            align-items: end;
        }
        
        .view-toggle {
            display: flex;
            gap: var(--space-2);
            margin-bottom: var(--space-4);
        }
        
        .view-btn {
            padding: var(--space-2) var(--space-4);
            border: 1px solid var(--gray-300);
            background: white;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.15s ease;
        }
        
        .view-btn.active {
            background: var(--primary-500);
            color: white;
            border-color: var(--primary-500);
        }
        
        .stats-overview {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--space-4);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--primary-600);
        }
        
        .stat-label {
            font-size: var(--text-sm);
            color: var(--gray-600);
            margin-top: var(--space-1);
        }
        
        .pending { border-left: 4px solid var(--warning-500); }
        .in-progress { border-left: 4px solid var(--info-500); }
        .review { border-left: 4px solid var(--warning-500); }
        .done { border-left: 4px solid var(--success-500); }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">首页</a>
                <a href="knowledge-space.html" class="nav-link">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link active">标注工作流</a>
                <a href="quality-control.html" class="nav-link">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item active">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">标注任务管理</h1>
                    <p class="text-gray-600">管理和跟踪标注任务的执行进度</p>
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-outline" onclick="exportTasks()">
                        📤 导出任务
                    </button>
                    <button class="btn btn-primary" onclick="location.href='annotation-tasks-form.html'">
                        ➕ 创建任务
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-item">
                <div class="stat-number">89</div>
                <div class="stat-label">总任务数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">23</div>
                <div class="stat-label">待开始</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">45</div>
                <div class="stat-label">进行中</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">12</div>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">9</div>
                <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">78.5%</div>
                <div class="stat-label">完成率</div>
            </div>
        </div>

        <!-- 视图切换 -->
        <div class="view-toggle">
            <button class="view-btn active" onclick="switchView('kanban')">📋 看板视图</button>
            <button class="view-btn" onclick="switchView('list')">📄 列表视图</button>
            <button class="view-btn" onclick="switchView('calendar')">📅 日历视图</button>
        </div>

        <!-- 筛选条件 -->
        <div class="filters-bar">
            <div class="form-group">
                <label class="label">搜索任务</label>
                <input type="text" class="input" placeholder="输入任务名称或ID" id="search-input">
            </div>
            <div class="form-group">
                <label class="label">任务类型(task_type)</label>
                <select class="select" id="type-filter">
                    <option value="">全部类型</option>
                    <option value="0">题-知识点标注</option>
                    <option value="1">题-题关系标注</option>
                    <option value="2">先修关系标注</option>
                </select>
            </div>
            <div class="form-group">
                <label class="label">任务状态(status)</label>
                <select class="select" id="status-filter">
                    <option value="">全部状态</option>
                    <option value="0">待开始</option>
                    <option value="1">进行中</option>
                    <option value="2">已完成</option>
                    <option value="3">已取消</option>
                    <option value="4">待审核</option>
                    <option value="5">审核通过</option>
                    <option value="6">审核拒绝</option>
                </select>
            </div>
            <div class="form-group">
                <label class="label">优先级(priority)</label>
                <select class="select" id="priority-filter">
                    <option value="">全部优先级</option>
                    <option value="1">低</option>
                    <option value="2">中</option>
                    <option value="3">高</option>
                    <option value="4">紧急</option>
                </select>
            </div>
            <div class="form-group">
                <label class="label">负责人(assigned_to)</label>
                <select class="select" id="assignee-filter">
                    <option value="">全部人员</option>
                    <option value="user_1001">张三</option>
                    <option value="user_1002">李四</option>
                    <option value="user_1003">王五</option>
                    <option value="user_1004">赵六</option>
                </select>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" onclick="applyFilters()">筛选</button>
                <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
            </div>
        </div>

        <!-- 看板视图 -->
        <div class="task-board" id="kanban-view">
            <!-- 待开始 -->
            <div class="task-column">
                <div class="column-header">
                    <span class="column-title">📋 待开始</span>
                    <span class="column-count">23</span>
                </div>
                
                <div class="task-card pending" onclick="viewTask('T001')">
                    <div class="task-header">
                        <span class="task-id">T001</span>
                        <span class="task-priority priority-high">高</span>
                    </div>
                    <div class="task-title">英语语法知识点标注</div>
                    <div class="task-description">对英语语法相关的200道题目进行知识点标注，包括时态、语态、从句等内容。</div>
                    <div class="task-meta">
                        <div class="task-assignee">
                            <div class="assignee-avatar">张</div>
                            <span>张三</span>
                        </div>
                        <span>截止: 01-25</span>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-sm btn-primary" onclick="startTask('T001'); event.stopPropagation();">开始任务</button>
                        <button class="btn btn-sm btn-outline" onclick="editTask('T001'); event.stopPropagation();">编辑</button>
                    </div>
                </div>

                <div class="task-card pending" onclick="viewTask('T002')">
                    <div class="task-header">
                        <span class="task-id">T002</span>
                        <span class="task-priority priority-medium">中</span>
                    </div>
                    <div class="task-title">数学函数关系标注</div>
                    <div class="task-description">标注数学函数题目之间的先修关系和递进关系。</div>
                    <div class="task-meta">
                        <div class="task-assignee">
                            <div class="assignee-avatar">李</div>
                            <span>李四</span>
                        </div>
                        <span>截止: 01-28</span>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-sm btn-primary" onclick="startTask('T002'); event.stopPropagation();">开始任务</button>
                        <button class="btn btn-sm btn-outline" onclick="editTask('T002'); event.stopPropagation();">编辑</button>
                    </div>
                </div>
            </div>

            <!-- 进行中 -->
            <div class="task-column">
                <div class="column-header">
                    <span class="column-title">🔄 进行中</span>
                    <span class="column-count">45</span>
                </div>
                
                <div class="task-card in-progress" onclick="viewTask('T003')">
                    <div class="task-header">
                        <span class="task-id">T003</span>
                        <span class="task-priority priority-high">高</span>
                    </div>
                    <div class="task-title">物理力学知识点标注</div>
                    <div class="task-description">对物理力学相关题目进行知识点标注和难度评估。</div>
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%;"></div>
                        </div>
                        <div class="progress-text">65% 完成 (130/200)</div>
                    </div>
                    <div class="task-meta">
                        <div class="task-assignee">
                            <div class="assignee-avatar">王</div>
                            <span>王五</span>
                        </div>
                        <span>截止: 01-22</span>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-sm btn-primary" onclick="continueTask('T003'); event.stopPropagation();">继续标注</button>
                        <button class="btn btn-sm btn-outline" onclick="pauseTask('T003'); event.stopPropagation();">暂停</button>
                    </div>
                </div>

                <div class="task-card in-progress" onclick="viewTask('T004')">
                    <div class="task-header">
                        <span class="task-id">T004</span>
                        <span class="task-priority priority-medium">中</span>
                    </div>
                    <div class="task-title">化学方程式先修关系</div>
                    <div class="task-description">建立化学方程式相关知识点的先修关系图。</div>
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 30%;"></div>
                        </div>
                        <div class="progress-text">30% 完成 (45/150)</div>
                    </div>
                    <div class="task-meta">
                        <div class="task-assignee">
                            <div class="assignee-avatar">赵</div>
                            <span>赵六</span>
                        </div>
                        <span>截止: 01-30</span>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-sm btn-primary" onclick="continueTask('T004'); event.stopPropagation();">继续标注</button>
                        <button class="btn btn-sm btn-outline" onclick="pauseTask('T004'); event.stopPropagation();">暂停</button>
                    </div>
                </div>
            </div>

            <!-- 待审核 -->
            <div class="task-column">
                <div class="column-header">
                    <span class="column-title">👁️ 待审核</span>
                    <span class="column-count">12</span>
                </div>
                
                <div class="task-card review" onclick="viewTask('T005')">
                    <div class="task-header">
                        <span class="task-id">T005</span>
                        <span class="task-priority priority-medium">中</span>
                    </div>
                    <div class="task-title">生物细胞知识点标注</div>
                    <div class="task-description">生物细胞结构和功能相关题目的知识点标注。</div>
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                        <div class="progress-text">100% 完成，待审核</div>
                    </div>
                    <div class="task-meta">
                        <div class="task-assignee">
                            <div class="assignee-avatar">张</div>
                            <span>张三</span>
                        </div>
                        <span>提交: 01-18</span>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-sm btn-primary" onclick="reviewTask('T005'); event.stopPropagation();">开始审核</button>
                        <button class="btn btn-sm btn-outline" onclick="viewTask('T005'); event.stopPropagation();">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 已完成 -->
            <div class="task-column">
                <div class="column-header">
                    <span class="column-title">✅ 已完成</span>
                    <span class="column-count">9</span>
                </div>
                
                <div class="task-card done" onclick="viewTask('T006')">
                    <div class="task-header">
                        <span class="task-id">T006</span>
                        <span class="task-priority priority-low">低</span>
                    </div>
                    <div class="task-title">历史年代知识点标注</div>
                    <div class="task-description">历史年代相关题目的知识点标注和时间线建立。</div>
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                        <div class="progress-text">已完成并审核通过</div>
                    </div>
                    <div class="task-meta">
                        <div class="task-assignee">
                            <div class="assignee-avatar">李</div>
                            <span>李四</span>
                        </div>
                        <span>完成: 01-15</span>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewTask('T006'); event.stopPropagation();">查看详情</button>
                        <button class="btn btn-sm btn-outline" onclick="downloadResults('T006'); event.stopPropagation();">下载结果</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 任务管理相关功能
        let currentView = 'kanban';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeTaskBoard();
        });

        // 初始化任务看板
        function initializeTaskBoard() {
            // 这里可以加载实际的任务数据
            console.log('初始化任务看板');
        }

        // 切换视图
        function switchView(view) {
            currentView = view;
            
            // 更新按钮状态
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 这里可以实现不同视图的切换逻辑
            if (view === 'list') {
                Message.info('切换到列表视图');
                // location.href = 'annotation-tasks-list.html';
            } else if (view === 'calendar') {
                Message.info('切换到日历视图');
                // location.href = 'annotation-tasks-calendar.html';
            } else {
                Message.info('切换到看板视图');
            }
        }

        // 应用筛选条件
        function applyFilters() {
            const searchTerm = document.getElementById('search-input').value;
            const type = document.getElementById('type-filter').value;
            const priority = document.getElementById('priority-filter').value;
            const assignee = document.getElementById('assignee-filter').value;
            
            console.log('应用筛选:', { searchTerm, type, priority, assignee });
            Message.info('筛选条件已应用');
        }

        // 重置筛选条件
        function resetFilters() {
            document.getElementById('search-input').value = '';
            document.getElementById('type-filter').value = '';
            document.getElementById('priority-filter').value = '';
            document.getElementById('assignee-filter').value = '';
            
            applyFilters();
        }

        // 查看任务详情
        function viewTask(taskId) {
            location.href = `annotation-task-detail.html?id=${taskId}`;
        }

        // 开始任务
        function startTask(taskId) {
            Modal.confirm(
                '开始任务',
                `确定要开始任务 ${taskId} 吗？`,
                () => {
                    console.log('开始任务:', taskId);
                    Message.success('任务已开始');
                    // 这里可以更新任务状态
                }
            );
        }

        // 继续任务
        function continueTask(taskId) {
            location.href = `annotation-workspace.html?task=${taskId}`;
        }

        // 暂停任务
        function pauseTask(taskId) {
            Modal.confirm(
                '暂停任务',
                `确定要暂停任务 ${taskId} 吗？`,
                () => {
                    console.log('暂停任务:', taskId);
                    Message.success('任务已暂停');
                }
            );
        }

        // 编辑任务
        function editTask(taskId) {
            location.href = `annotation-tasks-form.html?id=${taskId}`;
        }

        // 审核任务
        function reviewTask(taskId) {
            location.href = `annotation-review.html?task=${taskId}`;
        }

        // 下载结果
        function downloadResults(taskId) {
            Message.info('正在准备下载文件...');
            setTimeout(() => {
                Message.success('文件下载已开始');
            }, 1000);
        }

        // 导出任务
        function exportTasks() {
            Message.info('正在导出任务数据...');
            setTimeout(() => {
                Message.success('任务数据导出成功');
            }, 2000);
        }

        // 拖拽功能（可选实现）
        function initializeDragAndDrop() {
            // 这里可以实现任务卡片的拖拽功能
            // 允许在不同状态列之间拖拽任务卡片
        }
    </script>
</body>
</html>
