<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识点管理 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .search-filters {
            background: white;
            padding: var(--space-6);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--space-6);
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            align-items: end;
        }
        
        .tree-view {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            max-height: 400px;
            overflow-y: auto;
        }
        
        .tree-node {
            padding: var(--space-2) var(--space-4);
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: background-color 0.15s ease;
        }
        
        .tree-node:hover {
            background-color: var(--gray-50);
        }
        
        .tree-node.selected {
            background-color: var(--primary-50);
            color: var(--primary-700);
        }
        
        .tree-node-content {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .tree-toggle {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .tree-icon {
            color: var(--gray-400);
        }
        
        .tree-label {
            flex: 1;
        }
        
        .tree-actions {
            display: flex;
            gap: var(--space-1);
            opacity: 0;
            transition: opacity 0.15s ease;
        }
        
        .tree-node:hover .tree-actions {
            opacity: 1;
        }
        
        .level-1 { padding-left: var(--space-8); }
        .level-2 { padding-left: var(--space-12); }
        .level-3 { padding-left: var(--space-16); }
        
        .knowledge-point-detail {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
        }
        
        .detail-section {
            margin-bottom: var(--space-6);
        }
        
        .detail-section:last-child {
            margin-bottom: 0;
        }
        
        .detail-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-3);
            color: var(--gray-900);
        }
        
        .detail-item {
            display: flex;
            margin-bottom: var(--space-2);
        }
        
        .detail-label {
            width: 100px;
            font-weight: var(--font-medium);
            color: var(--gray-600);
        }
        
        .detail-value {
            flex: 1;
            color: var(--gray-900);
        }
        
        .prerequisite-list {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-2);
        }
        
        .prerequisite-item {
            background: var(--primary-100);
            color: var(--primary-800);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-md);
            font-size: var(--text-sm);
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">首页</a>
                <a href="knowledge-space.html" class="nav-link active">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link">标注工作流</a>
                <a href="quality-control.html" class="nav-link">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item active">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">知识点管理</h1>
                    <p class="text-gray-600">管理知识点层级结构和先修关系</p>
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-outline" onclick="exportKnowledgePoints()">
                        📤 导出
                    </button>
                    <button class="btn btn-outline" onclick="importKnowledgePoints()">
                        📥 导入
                    </button>
                    <button class="btn btn-primary" onclick="location.href='knowledge-points-form.html'">
                        ➕ 新建知识点
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filters">
            <div class="filter-row">
                <div class="form-group">
                    <label class="label">搜索知识点</label>
                    <input type="text" class="input" placeholder="输入知识点名称或编码" id="search-input">
                </div>
                <div class="form-group">
                    <label class="label">学科分类</label>
                    <select class="select" id="subject-filter">
                        <option value="">全部学科</option>
                        <option value="math">数学</option>
                        <option value="english">英语</option>
                        <option value="physics">物理</option>
                        <option value="chemistry">化学</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="label">难度等级</label>
                    <select class="select" id="difficulty-filter">
                        <option value="">全部难度</option>
                        <option value="1">1级 - 基础</option>
                        <option value="2">2级 - 简单</option>
                        <option value="3">3级 - 中等</option>
                        <option value="4">4级 - 困难</option>
                        <option value="5">5级 - 极难</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="label">状态</label>
                    <select class="select" id="status-filter">
                        <option value="">全部状态</option>
                        <option value="active">启用</option>
                        <option value="inactive">禁用</option>
                        <option value="draft">草稿</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="applyFilters()">搜索</button>
                    <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                </div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-6);">
            <!-- 知识点树形结构 -->
            <div class="card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h2 class="card-title">知识点层级结构</h2>
                        <div class="flex gap-2">
                            <button class="btn btn-sm btn-outline" onclick="expandAll()">展开全部</button>
                            <button class="btn btn-sm btn-outline" onclick="collapseAll()">收起全部</button>
                        </div>
                    </div>
                </div>
                <div class="tree-view" id="knowledge-tree">
                    <!-- 数学 -->
                    <div class="tree-node" data-id="1">
                        <div class="tree-node-content">
                            <span class="tree-toggle">▼</span>
                            <span class="tree-icon">📚</span>
                            <span class="tree-label">数学</span>
                            <div class="tree-actions">
                                <button class="btn btn-sm" onclick="editKnowledgePoint(1)">✏️</button>
                                <button class="btn btn-sm" onclick="deleteKnowledgePoint(1)">🗑️</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 代数 -->
                    <div class="tree-node level-1" data-id="2" data-parent="1">
                        <div class="tree-node-content">
                            <span class="tree-toggle">▼</span>
                            <span class="tree-icon">📖</span>
                            <span class="tree-label">代数</span>
                            <div class="tree-actions">
                                <button class="btn btn-sm" onclick="editKnowledgePoint(2)">✏️</button>
                                <button class="btn btn-sm" onclick="deleteKnowledgePoint(2)">🗑️</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 一元一次方程 -->
                    <div class="tree-node level-2 selected" data-id="3" data-parent="2">
                        <div class="tree-node-content">
                            <span class="tree-toggle"></span>
                            <span class="tree-icon">📄</span>
                            <span class="tree-label">一元一次方程</span>
                            <div class="tree-actions">
                                <button class="btn btn-sm" onclick="editKnowledgePoint(3)">✏️</button>
                                <button class="btn btn-sm" onclick="deleteKnowledgePoint(3)">🗑️</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 一元二次方程 -->
                    <div class="tree-node level-2" data-id="4" data-parent="2">
                        <div class="tree-node-content">
                            <span class="tree-toggle"></span>
                            <span class="tree-icon">📄</span>
                            <span class="tree-label">一元二次方程</span>
                            <div class="tree-actions">
                                <button class="btn btn-sm" onclick="editKnowledgePoint(4)">✏️</button>
                                <button class="btn btn-sm" onclick="deleteKnowledgePoint(4)">🗑️</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 几何 -->
                    <div class="tree-node level-1" data-id="5" data-parent="1">
                        <div class="tree-node-content">
                            <span class="tree-toggle">▶</span>
                            <span class="tree-icon">📖</span>
                            <span class="tree-label">几何</span>
                            <div class="tree-actions">
                                <button class="btn btn-sm" onclick="editKnowledgePoint(5)">✏️</button>
                                <button class="btn btn-sm" onclick="deleteKnowledgePoint(5)">🗑️</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 英语 -->
                    <div class="tree-node" data-id="6">
                        <div class="tree-node-content">
                            <span class="tree-toggle">▶</span>
                            <span class="tree-icon">📚</span>
                            <span class="tree-label">英语</span>
                            <div class="tree-actions">
                                <button class="btn btn-sm" onclick="editKnowledgePoint(6)">✏️</button>
                                <button class="btn btn-sm" onclick="deleteKnowledgePoint(6)">🗑️</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 知识点详情 -->
            <div class="knowledge-point-detail">
                <div class="detail-section">
                    <div class="detail-title">基本信息</div>
                    <div class="detail-item">
                        <span class="detail-label">知识点ID:</span>
                        <span class="detail-value">KP_003</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">名称:</span>
                        <span class="detail-value">一元一次方程</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">编码:</span>
                        <span class="detail-value">MATH_ALG_001</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">难度等级:</span>
                        <span class="detail-value">
                            <span class="badge badge-warning">3级 - 中等</span>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">状态:</span>
                        <span class="detail-value">
                            <span class="badge badge-success">启用</span>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">创建时间:</span>
                        <span class="detail-value">2024-01-15 10:30:00</span>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">描述信息</div>
                    <div class="detail-item">
                        <span class="detail-label">描述:</span>
                        <span class="detail-value">
                            一元一次方程是只含有一个未知数，并且未知数的最高次数是1的方程。
                            它是代数学习的基础内容，为后续学习二次方程等内容奠定基础。
                        </span>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">先修知识点</div>
                    <div class="prerequisite-list">
                        <span class="prerequisite-item">有理数运算</span>
                        <span class="prerequisite-item">代数式</span>
                        <span class="prerequisite-item">等式性质</span>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">关联题目</div>
                    <div class="detail-item">
                        <span class="detail-label">关联题目数:</span>
                        <span class="detail-value">156题</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">平均难度:</span>
                        <span class="detail-value">2.8</span>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="flex gap-2">
                        <button class="btn btn-primary" onclick="editKnowledgePoint(3)">
                            ✏️ 编辑
                        </button>
                        <button class="btn btn-outline" onclick="viewPrerequisites(3)">
                            🔗 查看先修关系
                        </button>
                        <button class="btn btn-outline" onclick="viewRelatedQuestions(3)">
                            📝 查看关联题目
                        </button>
                        <button class="btn btn-danger" onclick="deleteKnowledgePoint(3)">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 知识点管理相关功能
        let selectedKnowledgePoint = null;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializeTreeView();
            loadKnowledgePointDetail(3); // 默认选中第一个知识点
        });

        // 初始化树形视图
        function initializeTreeView() {
            const treeNodes = document.querySelectorAll('.tree-node');
            
            treeNodes.forEach(node => {
                node.addEventListener('click', function(e) {
                    if (e.target.classList.contains('tree-toggle')) {
                        toggleNode(this);
                    } else {
                        selectNode(this);
                    }
                });
            });
        }

        // 切换节点展开/收起
        function toggleNode(node) {
            const toggle = node.querySelector('.tree-toggle');
            const nodeId = node.dataset.id;
            const childNodes = document.querySelectorAll(`[data-parent="${nodeId}"]`);
            
            if (toggle.textContent === '▼') {
                toggle.textContent = '▶';
                childNodes.forEach(child => child.style.display = 'none');
            } else {
                toggle.textContent = '▼';
                childNodes.forEach(child => child.style.display = 'block');
            }
        }

        // 选择节点
        function selectNode(node) {
            // 移除之前的选中状态
            document.querySelectorAll('.tree-node').forEach(n => n.classList.remove('selected'));
            
            // 添加选中状态
            node.classList.add('selected');
            
            // 加载知识点详情
            const nodeId = parseInt(node.dataset.id);
            loadKnowledgePointDetail(nodeId);
        }

        // 加载知识点详情
        function loadKnowledgePointDetail(id) {
            selectedKnowledgePoint = id;
            // 这里可以通过API加载实际数据
            console.log('加载知识点详情:', id);
        }

        // 展开全部节点
        function expandAll() {
            document.querySelectorAll('.tree-toggle').forEach(toggle => {
                if (toggle.textContent === '▶') {
                    toggle.click();
                }
            });
        }

        // 收起全部节点
        function collapseAll() {
            document.querySelectorAll('.tree-toggle').forEach(toggle => {
                if (toggle.textContent === '▼') {
                    toggle.click();
                }
            });
        }

        // 应用筛选条件
        function applyFilters() {
            const searchTerm = document.getElementById('search-input').value;
            const subject = document.getElementById('subject-filter').value;
            const difficulty = document.getElementById('difficulty-filter').value;
            const status = document.getElementById('status-filter').value;
            
            console.log('应用筛选:', { searchTerm, subject, difficulty, status });
            
            // 这里实现筛选逻辑
            Message.info('筛选条件已应用');
        }

        // 重置筛选条件
        function resetFilters() {
            document.getElementById('search-input').value = '';
            document.getElementById('subject-filter').value = '';
            document.getElementById('difficulty-filter').value = '';
            document.getElementById('status-filter').value = '';
            
            applyFilters();
        }

        // 编辑知识点
        function editKnowledgePoint(id) {
            location.href = `knowledge-points-form.html?id=${id}`;
        }

        // 删除知识点
        function deleteKnowledgePoint(id) {
            Modal.confirm(
                '确认删除',
                '确定要删除这个知识点吗？删除后将无法恢复。',
                () => {
                    console.log('删除知识点:', id);
                    Message.success('知识点删除成功');
                }
            );
        }

        // 查看先修关系
        function viewPrerequisites(id) {
            location.href = `prerequisite-relations.html?id=${id}`;
        }

        // 查看关联题目
        function viewRelatedQuestions(id) {
            location.href = `questions.html?kp_id=${id}`;
        }

        // 导出知识点
        function exportKnowledgePoints() {
            Message.info('正在导出知识点数据...');
            // 模拟导出过程
            setTimeout(() => {
                Message.success('知识点数据导出成功');
            }, 2000);
        }

        // 导入知识点
        function importKnowledgePoints() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls,.csv';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    Message.info('正在导入知识点数据...');
                    // 模拟导入过程
                    setTimeout(() => {
                        Message.success('知识点数据导入成功');
                    }, 2000);
                }
            };
            input.click();
        }
    </script>
</body>
</html>
