# 知识空间构建模块设计

## 1. 功能描述

### 1.1 模块概述
知识空间构建模块是自适应学习服务数据标注系统的核心模块，负责基于人工标注驱动的知识空间构建。该模块通过教研专家和标注员的协作，建立题目与知识点的关联关系，维护知识点之间的先修关系，最终构建出完整的知识空间结构。

### 1.2 核心功能
- **题目-知识点关联标注**：建立题目与知识点的映射关系
- **知识点先修关系维护**：构建知识点之间的依赖关系图
- **题目-题目关系标注**：标注题目间的必备、递进、等价等关系
- **知识空间自动推理**：基于标注数据自动生成知识空间结构
- **可视化编辑界面**：提供直观的图形化编辑工具

### 1.3 业务价值
- 为自适应学习算法提供高质量的知识结构数据
- 支持个性化学习路径推荐
- 提高学习效果评估的准确性
- 为教学内容组织提供科学依据

## 2. 功能实现流程

### 2.1 整体工作流程

```mermaid
graph TD
    A[题目/知识点入库] --> B[标注任务生成]
    B --> C[标注员领取任务]
    C --> D[题-知识点关联标注]
    D --> E[知识点先修关系标注]
    E --> F[题-题关系标注]
    F --> G[QA审核]
    G --> H{审核通过?}
    H -->|否| I[返回修改]
    I --> C
    H -->|是| J[数据归档发布]
    J --> K[触发Dapr事件]
    K --> L[知识空间重构]
```

### 2.2 题目-知识点关联流程

#### 2.2.1 标注准备
1. **题目预处理**：解析题目内容，提取关键信息
2. **知识点树加载**：展示完整的知识点层级结构
3. **相关知识点推荐**：基于题目内容智能推荐可能相关的知识点

#### 2.2.2 标注执行
1. **题目内容展示**：富文本渲染题干、选项、解析
2. **知识点选择**：从知识点树中选择相关知识点
3. **关联强度标注**：设置题目与知识点的关联置信度
4. **标注验证**：实时检查标注的完整性和合理性

#### 2.2.3 标注提交
1. **数据校验**：检查必填字段和业务规则
2. **冲突检测**：识别与已有标注的冲突
3. **提交确认**：显示标注摘要，确认提交

### 2.3 知识点先修关系维护流程

#### 2.3.1 关系图编辑
1. **图形化界面**：提供拖拽式的关系图编辑器
2. **节点操作**：添加、删除、移动知识点节点
3. **边操作**：创建、删除先修关系边
4. **实时验证**：检测环路和逻辑冲突

#### 2.3.2 批量导入
1. **模板下载**：提供标准的Excel模板
2. **数据导入**：支持批量导入先修关系
3. **数据验证**：检查导入数据的格式和逻辑
4. **冲突解决**：处理导入数据与现有数据的冲突

### 2.4 知识空间生成流程

#### 2.4.1 数据准备
1. **关系数据收集**：汇总所有标注的关系数据
2. **数据清洗**：去除重复和无效的关系
3. **一致性检查**：确保关系数据的逻辑一致性

#### 2.4.2 空间构建
1. **闭包计算**：使用Next-Closure算法计算知识空间
2. **状态生成**：生成所有可能的知识状态
3. **转移关系**：构建知识状态间的转移关系
4. **结果验证**：验证生成的知识空间的合理性

## 3. 业务规则

### 3.1 题目-知识点关联规则

#### 3.1.1 基本规则
- **最少关联数**：每个题目必须关联至少1个知识点
- **最多关联数**：每个题目最多关联5个知识点
- **置信度范围**：关联置信度必须在0.6-1.0之间
- **低置信度备注**：置信度低于0.8时必须提供备注说明

#### 3.1.2 特殊规则
- **多媒体题目**：包含阅读材料的题目需要标注文章级和小题级知识点
- **复合题目**：一题多问的题目每个子问题独立标注知识点
- **知识点层级**：只能关联叶子节点知识点，不能关联父级节点

### 3.2 知识点先修关系规则

#### 3.2.1 逻辑规则
- **无环约束**：先修关系图不能形成环路
- **传递性**：如果A→B，B→C，则隐含A→C，不需要显式标注
- **最小化原则**：避免冗余的先修关系，保持图的简洁性

#### 3.2.2 粒度规则
- **同级限制**：只允许同一层级的知识点建立先修关系
- **父级继承**：子知识点的先修关系自动继承到父级
- **叶子节点优先**：优先在叶子节点建立先修关系

### 3.3 题目-题目关系规则

#### 3.3.1 关系类型定义
- **prerequisite**：先修关系，A题是B题的前置要求
- **progresses_to**：递进关系，A题到B题是知识的递进
- **equivalent**：等价关系，A题和B题考查相同知识点
- **complements**：互补关系，A题和B题互为补充
- **revision**：修订关系，B题是A题的修订版本

#### 3.3.2 约束规则
- **先修题覆盖**：先修题的知识点必须完全覆盖后续题的知识点
- **难度单调**：先修题的IRT难度不能高于后续题
- **等价互斥**：等价关系与先修关系互斥，不能同时存在
- **双向限制**：同一对题目只能建立一种关系类型

### 3.4 质量控制规则

#### 3.4.1 一致性检查
- **标注员一致性**：同一题目由多个标注员标注时，一致性要求≥85%
- **专家校验**：知识结构专家定期校验标注质量
- **交叉验证**：随机抽取10%的标注进行交叉验证

#### 3.4.2 异常检测
- **孤儿知识点**：没有任何题目关联的知识点
- **孤儿题目**：没有关联任何知识点的题目
- **异常难度**：IRT难度与先修关系不符的题目对
- **环路检测**：先修关系图中的环路

## 4. 使用角色

### 4.1 内容标注员

#### 4.1.1 角色职责
- 执行题目-知识点关联标注
- 标注题目间的关系
- 进行题目难度的初步标注
- 维护标注数据的质量

#### 4.1.2 技能要求
- 具备相关学科的专业知识
- 熟悉题目类型和考查要点
- 具备基本的计算机操作能力
- 理解知识点体系结构

#### 4.1.3 权限范围
- 查看分配的标注任务
- 编辑题目-知识点关联
- 标注题目间关系
- 提交标注结果

### 4.2 知识结构专家

#### 4.2.1 角色职责
- 维护知识点层级结构
- 构建和优化先修关系图
- 解决标注冲突和争议
- 制定标注标准和规范

#### 4.2.2 技能要求
- 深厚的学科专业背景
- 教学经验和课程设计能力
- 理解认知科学和学习理论
- 具备数据分析能力

#### 4.2.3 权限范围
- 管理知识点体系
- 编辑先修关系图
- 审核标注质量
- 制定业务规则

### 4.3 审核员(QA)

#### 4.3.1 角色职责
- 复核所有标注内容
- 进行一致性检验
- 识别和处理异常数据
- 维护数据质量标准

#### 4.3.2 技能要求
- 质量管理经验
- 数据分析能力
- 细致的工作态度
- 沟通协调能力

#### 4.3.3 权限范围
- 查看所有标注数据
- 审核标注质量
- 退回不合格标注
- 生成质量报告

### 4.4 数据发布员

#### 4.4.1 角色职责
- 管理数据发布流程
- 维护版本控制
- 监控系统集成
- 处理数据回滚

#### 4.4.2 技能要求
- 数据工程背景
- 版本管理经验
- 系统集成知识
- 问题排查能力

#### 4.4.3 权限范围
- 发布审核通过的数据
- 管理数据版本
- 配置系统集成
- 执行数据回滚

## 5. 界面设计要求

### 5.1 标注工作台界面

#### 5.1.1 布局设计
```
┌─────────────────────────────────────────────────────────┐
│                    导航栏                                │
├─────────────────┬───────────────────┬───────────────────┤
│                 │                   │                   │
│   题目预览区    │   知识点选择区    │   关系标注区      │
│                 │                   │                   │
│   - 题干展示    │   - 知识点树      │   - 题题关系      │
│   - 选项展示    │   - 搜索框        │   - 置信度设置    │
│   - 媒体播放    │   - 已选标签      │   - 备注信息      │
│                 │                   │                   │
├─────────────────┴───────────────────┴───────────────────┤
│                    操作按钮区                            │
└─────────────────────────────────────────────────────────┘
```

#### 5.1.2 交互要求
- **题目预览**：支持富文本渲染、图片放大、音频播放
- **知识点选择**：树状结构展示，支持搜索和快捷键操作
- **拖拽操作**：支持知识点的拖拽选择
- **实时保存**：标注内容自动保存，避免数据丢失

### 5.2 先修关系编辑界面

#### 5.2.1 图形编辑器
- **画布区域**：可缩放的图形编辑画布
- **节点操作**：支持节点的添加、删除、移动
- **连线操作**：支持拖拽创建先修关系
- **工具栏**：提供常用的编辑工具

#### 5.2.2 侧边面板
- **知识点列表**：显示所有知识点
- **属性编辑**：编辑选中节点的属性
- **关系列表**：显示所有先修关系
- **验证结果**：显示图形验证的结果

### 5.3 质量检测界面

#### 5.3.1 仪表板
- **质量指标**：显示关键的质量指标
- **异常统计**：统计各类异常数据
- **趋势图表**：显示质量趋势变化
- **告警信息**：显示需要关注的问题

#### 5.3.2 详细报告
- **问题列表**：列出所有检测到的问题
- **问题详情**：显示问题的具体信息
- **处理建议**：提供问题的处理建议
- **处理记录**：记录问题的处理过程

### 5.4 响应式设计要求

#### 5.4.1 桌面端(≥1024px)
- 三栏布局，充分利用屏幕空间
- 支持多窗口并行操作
- 提供丰富的快捷键支持

#### 5.4.2 平板端(768px-1023px)
- 两栏布局，可折叠侧边栏
- 优化触摸操作体验
- 简化复杂的交互操作

#### 5.4.3 移动端(<768px)
- 单栏布局，分步骤操作
- 大按钮设计，便于触摸
- 简化功能，突出核心操作

### 5.5 可访问性要求

#### 5.5.1 键盘导航
- 所有功能支持键盘操作
- 合理的Tab键顺序
- 明确的焦点指示

#### 5.5.2 屏幕阅读器
- 语义化的HTML结构
- 完整的ARIA标签
- 图片和图表的文字描述

#### 5.5.3 视觉辅助
- 高对比度模式支持
- 字体大小可调节
- 色盲友好的配色方案
